#!/usr/bin/env python3
"""
快速启动脚本 - Rovo Dev API
"""

import os
import sys
import subprocess
import argparse
import time
import requests
from pathlib import Path


def check_dependencies():
    """检查依赖项"""
    print("检查依赖项...")
    
    # 检查 Python 版本
    if sys.version_info < (3, 8):
        print("❌ 需要 Python 3.8 或更高版本")
        return False
    
    print(f"✅ Python 版本: {sys.version}")
    
    # 检查 acli 命令
    try:
        result = subprocess.run(["acli", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Atlassian CLI 已安装")
        else:
            print("❌ Atlassian CLI 未正确安装")
            return False
    except FileNotFoundError:
        print("❌ 未找到 Atlassian CLI (acli)")
        print("请先安装 Atlassian CLI: https://developer.atlassian.com/platform/atlassian-cli/")
        return False
    
    return True


def install_dependencies():
    """安装 Python 依赖"""
    print("安装 Python 依赖...")
    
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True)
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False


def check_rovo_dev_access():
    """检查 Rovo Dev 访问权限"""
    print("检查 Rovo Dev 访问权限...")
    
    try:
        # 尝试启动 Rovo Dev 会话并快速退出
        process = subprocess.Popen(
            ["acli", "rovodev", "run"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待一下让它启动
        time.sleep(3)
        
        # 发送退出命令
        process.stdin.write("/exit\n")
        process.stdin.flush()
        
        # 等待进程结束
        try:
            process.wait(timeout=10)
            print("✅ Rovo Dev 访问正常")
            return True
        except subprocess.TimeoutExpired:
            process.terminate()
            print("⚠️  Rovo Dev 响应较慢，但可能可用")
            return True
            
    except Exception as e:
        print(f"❌ Rovo Dev 访问检查失败: {e}")
        print("请确保您有有效的 Rovo Dev 访问权限")
        return False


def start_server(host="0.0.0.0", port=8000, reload=False):
    """启动服务器"""
    print(f"启动 Rovo Dev API 服务器 (http://{host}:{port})...")
    
    cmd = [
        sys.executable, "-m", "rovo_dev_api.main",
        "--host", host,
        "--port", str(port)
    ]
    
    if reload:
        cmd.append("--reload")
    
    try:
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\n服务器已停止")


def test_server(host="localhost", port=8000, timeout=30):
    """测试服务器是否正常运行"""
    print(f"测试服务器连接 (http://{host}:{port})...")
    
    url = f"http://{host}:{port}/"
    
    for i in range(timeout):
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print("✅ 服务器运行正常")
                return True
        except requests.exceptions.RequestException:
            pass
        
        if i < timeout - 1:
            print(f"等待服务器启动... ({i+1}/{timeout})")
            time.sleep(1)
    
    print("❌ 服务器连接失败")
    return False


def run_example():
    """运行示例"""
    print("运行基本使用示例...")
    
    try:
        subprocess.run([sys.executable, "examples/basic_usage.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"示例运行失败: {e}")
    except FileNotFoundError:
        print("未找到示例文件")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Rovo Dev API 快速启动脚本")
    parser.add_argument("--host", default="0.0.0.0", help="服务器主机地址")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口")
    parser.add_argument("--reload", action="store_true", help="启用开发模式（自动重载）")
    parser.add_argument("--skip-checks", action="store_true", help="跳过依赖检查")
    parser.add_argument("--install-deps", action="store_true", help="安装依赖")
    parser.add_argument("--test-only", action="store_true", help="仅测试服务器")
    parser.add_argument("--example", action="store_true", help="运行示例")
    
    args = parser.parse_args()
    
    print("🚀 Rovo Dev API 启动器")
    print("=" * 50)
    
    # 安装依赖
    if args.install_deps:
        if not install_dependencies():
            sys.exit(1)
        return
    
    # 检查依赖
    if not args.skip_checks:
        if not check_dependencies():
            print("\n💡 提示: 使用 --install-deps 安装 Python 依赖")
            sys.exit(1)
        
        if not check_rovo_dev_access():
            print("\n💡 提示: 请确保已正确配置 Atlassian CLI 和 Rovo Dev 访问权限")
            sys.exit(1)
    
    # 仅测试模式
    if args.test_only:
        if test_server(args.host, args.port):
            if args.example:
                run_example()
        return
    
    # 启动服务器
    print("\n🎯 所有检查通过，启动服务器...")
    print(f"📍 服务地址: http://{args.host}:{args.port}")
    print("📖 API 文档: http://{args.host}:{args.port}/docs")
    print("⏹️  按 Ctrl+C 停止服务器")
    print("-" * 50)
    
    try:
        start_server(args.host, args.port, args.reload)
    except KeyboardInterrupt:
        print("\n👋 再见！")


if __name__ == "__main__":
    main()
