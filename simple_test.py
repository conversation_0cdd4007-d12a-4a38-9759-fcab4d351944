#!/usr/bin/env python3
"""
简单的 API 测试，不依赖 Rovo Dev 进程
"""

from fastapi import FastAPI
from fastapi.responses import JSONResponse
import uvicorn


# 创建一个简单的测试 API
app = FastAPI(title="Test API")


@app.get("/")
async def root():
    return {"message": "Test API", "status": "running"}


@app.post("/v1/chat/completions")
async def test_chat_completion(request: dict):
    """测试聊天完成端点"""
    
    # 模拟响应
    response = {
        "id": "test-123",
        "object": "chat.completion",
        "created": 1234567890,
        "model": "test-model",
        "choices": [
            {
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": "这是一个测试响应。你发送的消息是: " + str(request.get("messages", []))
                },
                "finish_reason": "stop"
            }
        ],
        "usage": {
            "prompt_tokens": 10,
            "completion_tokens": 20,
            "total_tokens": 30
        }
    }
    
    return JSONResponse(content=response)


if __name__ == "__main__":
    print("启动测试 API 服务器...")
    uvicorn.run(app, host="127.0.0.1", port=8002)
