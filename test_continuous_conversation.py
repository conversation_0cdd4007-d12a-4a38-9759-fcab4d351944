#!/usr/bin/env python3
"""
测试 Rovo Dev API 的持续对话功能
"""

import openai
import time


def test_continuous_conversation():
    """测试持续对话"""
    print("🔄 测试 Rovo Dev API 持续对话功能")
    print("=" * 50)
    
    # 配置 OpenAI 客户端
    client = openai.OpenAI(
        base_url="http://localhost:8000/v1",
        api_key="dummy-key"  # 我们的 API 不需要真实的 key
    )
    
    # 定义一个持续的对话场景
    conversation_scenarios = [
        {
            "name": "编程助手对话",
            "messages": [
                {"role": "system", "content": "你是一个专业的 Python 编程助手"},
                {"role": "user", "content": "你好，我想学习 Python"},
                {"role": "user", "content": "请帮我写一个计算斐波那契数列的函数"},
                {"role": "user", "content": "能否解释一下这个函数的时间复杂度？"}
            ]
        },
        {
            "name": "代码审查对话",
            "messages": [
                {"role": "system", "content": "你是一个代码审查专家"},
                {"role": "user", "content": "请帮我审查这段代码"},
                {"role": "user", "content": "有什么可以优化的地方吗？"},
                {"role": "user", "content": "性能方面有什么建议？"}
            ]
        },
        {
            "name": "问题解决对话",
            "messages": [
                {"role": "user", "content": "我遇到了一个 bug"},
                {"role": "user", "content": "错误信息是 'list index out of range'"},
                {"role": "user", "content": "应该如何调试这个问题？"}
            ]
        }
    ]
    
    for scenario in conversation_scenarios:
        print(f"\n🎭 场景: {scenario['name']}")
        print("-" * 30)
        
        # 模拟持续对话
        conversation_history = []
        
        for i, message in enumerate(scenario['messages']):
            print(f"\n💬 轮次 {i+1}")
            
            # 添加到对话历史
            conversation_history.append(message)
            
            print(f"👤 {message['role']}: {message['content']}")
            
            try:
                # 发送整个对话历史
                response = client.chat.completions.create(
                    model="rovo-dev",
                    messages=conversation_history,
                    temperature=0.7
                )
                
                assistant_response = response.choices[0].message.content
                print(f"🤖 助手: {assistant_response}")
                
                # 将助手回复添加到对话历史
                conversation_history.append({
                    "role": "assistant", 
                    "content": assistant_response
                })
                
                print(f"📊 Token 使用: {response.usage}")
                
                # 短暂延迟，模拟真实对话
                time.sleep(1)
                
            except Exception as e:
                print(f"❌ 请求失败: {e}")
                break
        
        print(f"\n✅ {scenario['name']} 完成")
        print(f"📝 对话轮次: {len(conversation_history) // 2}")


def test_streaming_conversation():
    """测试流式持续对话"""
    print("\n\n🌊 测试流式持续对话")
    print("=" * 50)
    
    client = openai.OpenAI(
        base_url="http://localhost:8000/v1",
        api_key="dummy-key"
    )
    
    # 模拟一个技术讨论
    conversation = [
        {"role": "system", "content": "你是一个技术专家，善于解释复杂概念"},
        {"role": "user", "content": "什么是微服务架构？"},
    ]
    
    print("👤 用户: 什么是微服务架构？")
    print("🤖 助手 (流式): ", end="", flush=True)
    
    try:
        stream = client.chat.completions.create(
            model="rovo-dev",
            messages=conversation,
            stream=True,
            temperature=0.7
        )
        
        full_response = ""
        for chunk in stream:
            if chunk.choices[0].delta.content is not None:
                content = chunk.choices[0].delta.content
                print(content, end="", flush=True)
                full_response += content
        
        print("\n")
        
        # 继续对话
        conversation.append({"role": "assistant", "content": full_response})
        conversation.append({"role": "user", "content": "能举个具体的例子吗？"})
        
        print("\n👤 用户: 能举个具体的例子吗？")
        print("🤖 助手 (流式): ", end="", flush=True)
        
        stream = client.chat.completions.create(
            model="rovo-dev",
            messages=conversation,
            stream=True,
            temperature=0.7
        )
        
        for chunk in stream:
            if chunk.choices[0].delta.content is not None:
                content = chunk.choices[0].delta.content
                print(content, end="", flush=True)
        
        print("\n\n✅ 流式持续对话测试完成")
        
    except Exception as e:
        print(f"\n❌ 流式对话失败: {e}")


def test_context_awareness():
    """测试上下文感知能力"""
    print("\n\n🧠 测试上下文感知能力")
    print("=" * 50)
    
    client = openai.OpenAI(
        base_url="http://localhost:8000/v1",
        api_key="dummy-key"
    )
    
    # 测试上下文记忆
    messages = [
        {"role": "user", "content": "我的名字是张三，我是一名前端开发者"},
        {"role": "user", "content": "我刚才说我是做什么工作的？"},
        {"role": "user", "content": "根据我的工作，你推荐我学习什么技术？"}
    ]
    
    conversation_history = []
    
    for i, message in enumerate(messages):
        conversation_history.append(message)
        
        print(f"\n💬 问题 {i+1}: {message['content']}")
        
        try:
            response = client.chat.completions.create(
                model="rovo-dev",
                messages=conversation_history,
                temperature=0.7
            )
            
            assistant_response = response.choices[0].message.content
            print(f"🤖 回答: {assistant_response}")
            
            conversation_history.append({
                "role": "assistant",
                "content": assistant_response
            })
            
        except Exception as e:
            print(f"❌ 请求失败: {e}")
    
    print("\n✅ 上下文感知测试完成")


def main():
    """主函数"""
    print("🚀 Rovo Dev API 持续对话测试套件")
    print("=" * 60)
    
    # 首先检查 API 是否可用
    try:
        client = openai.OpenAI(
            base_url="http://localhost:8000/v1",
            api_key="dummy-key"
        )
        
        # 简单测试
        response = client.chat.completions.create(
            model="rovo-dev",
            messages=[{"role": "user", "content": "Hello"}]
        )
        
        print("✅ API 连接正常")
        
    except Exception as e:
        print(f"❌ API 连接失败: {e}")
        print("请确保 Rovo Dev API 服务正在运行在 http://localhost:8000")
        return
    
    # 运行测试
    test_continuous_conversation()
    test_streaming_conversation()
    test_context_awareness()
    
    print("\n🎉 所有持续对话测试完成！")
    print("\n💡 总结:")
    print("   ✅ 支持多轮对话")
    print("   ✅ 支持流式对话")
    print("   ✅ 支持上下文记忆")
    print("   ✅ 支持不同对话场景")


if __name__ == "__main__":
    main()
