#!/usr/bin/env python3
"""
测试各种不同的请求
Test various different requests
"""

import requests
import json

def test_request(content, description):
    """测试单个请求"""
    print(f"\n{'='*50}")
    print(f"测试: {description}")
    print(f"请求: {content}")
    print('='*50)
    
    response = requests.post(
        'http://localhost:8000/v1/chat/completions',
        headers={'Content-Type': 'application/json'},
        json={
            'model': 'rovo-dev',
            'messages': [{'role': 'user', 'content': content}],
            'stream': False
        },
        timeout=30
    )
    
    if response.status_code == 200:
        result = response.json()
        content_response = result['choices'][0]['message']['content']
        print(f'响应:\n{content_response[:200]}...')
    else:
        print(f'错误: {response.text}')

def main():
    """主测试函数"""
    test_cases = [
        ("你好", "问候语测试"),
        ("介绍一下你自己", "自我介绍测试"),
        ("写一个排序算法", "排序算法测试"),
        ("Python pandas 怎么用", "Python 库测试"),
        ("我的代码有 bug", "调试帮助测试"),
        ("如何学习编程", "学习指导测试")
    ]
    
    for content, description in test_cases:
        test_request(content, description)

if __name__ == '__main__':
    main()
