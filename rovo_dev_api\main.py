"""
Main entry point for Rovo Dev API
"""

import argparse
import uvicorn
from .config import get_settings, setup_logging
from .api import app


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Rovo Dev API Server")
    parser.add_argument(
        "--host",
        type=str,
        help="Host to bind to"
    )
    parser.add_argument(
        "--port",
        type=int,
        help="Port to bind to"
    )
    parser.add_argument(
        "--workers",
        type=int,
        help="Number of worker processes"
    )
    parser.add_argument(
        "--config",
        type=str,
        default="config.yaml",
        help="Path to configuration file"
    )
    parser.add_argument(
        "--reload",
        action="store_true",
        help="Enable auto-reload for development"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging()
    
    # Get settings
    settings = get_settings()
    
    # Override settings with command line arguments
    host = args.host or settings.server.host
    port = args.port or settings.server.port
    workers = args.workers or settings.server.workers
    
    # Run the server
    uvicorn.run(
        "rovo_dev_api.api:app",
        host=host,
        port=port,
        workers=workers if not args.reload else 1,
        reload=args.reload,
        log_level=settings.logging.level.lower()
    )


if __name__ == "__main__":
    main()
