"""
Custom exceptions for Rovo Dev API
"""


class RovoDevException(Exception):
    """Base exception for Rovo Dev related errors"""
    pass


class RovoDevConnectionError(RovoDevException):
    """Raised when connection to Rovo Dev fails"""
    pass


class RovoDevTimeoutError(RovoDevException):
    """Raised when Rovo Dev operation times out"""
    pass


class RovoDevProcessError(RovoDevException):
    """Raised when Rovo Dev process encounters an error"""
    pass


class RovoDevSessionError(RovoDevException):
    """Raised when Rovo Dev session is not active or invalid"""
    pass


class APIValidationError(Exception):
    """Raised when API request validation fails"""
    pass


class APIRateLimitError(Exception):
    """Raised when API rate limit is exceeded"""
    pass
