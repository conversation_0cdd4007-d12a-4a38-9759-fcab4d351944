"""
FastAPI application for Rovo Dev API
Rovo Dev API 的 FastAPI 应用程序
"""

import asyncio
import json
import logging
import uuid
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware

from .models import (
    ChatCompletionRequest,
    ChatCompletionResponse,
    ChatCompletionStreamResponse,
    ChatCompletionUsage,
    ErrorResponse
)
# 使用真正的 RovoDevClient 而不是简化版本
# Use the real RovoDevClient instead of the simplified version
from .rovo_client import RovoDevClient
from .message_converter import MessageConverter
from .config import get_settings
from .exceptions import (
    RovoDevException,
    RovoDevConnectionError,
    RovoDevTimeoutError,
    RovoDevProcessError,
    RovoDevSessionError,
    APIValidationError,
    APIRateLimitError
)

logger = logging.getLogger(__name__)

# 全局客户端实例（生产环境中建议使用依赖注入）
# Global client instance (in production, consider using dependency injection)
rovo_client: RovoDevClient = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    管理应用程序生命周期
    Manage application lifespan
    """
    global rovo_client

    # 启动阶段
    # Startup
    logger.info("Starting Rovo Dev API...")
    settings = get_settings()

    # 创建 Rovo Dev 客户端实例
    # Create Rovo Dev client instance
    rovo_client = RovoDevClient(
        command=settings.rovo_dev.command,
        timeout=settings.rovo_dev.timeout,
        working_directory=settings.rovo_dev.working_directory
    )

    # 启动会话
    # Start the session
    success = await rovo_client.start_session()
    if not success:
        logger.error("Failed to start Rovo Dev session")
        raise Exception("Failed to initialize Rovo Dev client")

    logger.info("Rovo Dev API started successfully")

    yield

    # 关闭阶段
    # Shutdown
    if rovo_client:
        await rovo_client.close_session()
        logger.info("Rovo Dev session closed")


app = FastAPI(
    title="Rovo Dev API",
    description="OpenAI-compatible API wrapper for Rovo Dev AI assistant",
    version="0.1.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """
    根端点 - 返回 API 基本信息
    Root endpoint - returns basic API information
    """
    return {
        "message": "Rovo Dev API",
        "version": "0.1.0",
        "status": "running"
    }


@app.get("/v1/models")
async def list_models():
    """
    列出可用模型（OpenAI 兼容性）
    List available models (OpenAI compatibility)
    """
    settings = get_settings()
    return {
        "object": "list",
        "data": [
            {
                "id": settings.api.model_name,
                "object": "model",
                "created": 1677610602,
                "owned_by": "rovo-dev"
            }
        ]
    }


@app.post("/v1/chat/completions")
async def create_chat_completion(request: ChatCompletionRequest):
    """
    创建聊天完成 - 主要的 AI 对话端点
    Create a chat completion - main AI conversation endpoint
    """
    global rovo_client

    # 验证请求参数
    # Validate request
    if not request.messages:
        raise HTTPException(
            status_code=400,
            detail="Messages cannot be empty"
        )

    if request.n != 1:
        raise HTTPException(
            status_code=400,
            detail="Only n=1 is supported"
        )

    if not rovo_client or not rovo_client.session_active:
        raise HTTPException(
            status_code=503,
            detail="Rovo Dev service unavailable"
        )

    try:
        # 将消息转换为 Rovo Dev 格式
        # Convert messages to Rovo Dev format
        prompt = MessageConverter.convert_messages_to_rovo_format(request.messages)

        if not prompt.strip():
            raise HTTPException(
                status_code=400,
                detail="Invalid message format"
            )

        # 根据请求类型返回流式或非流式响应
        # Return streaming or non-streaming response based on request type
        if request.stream:
            return StreamingResponse(
                stream_chat_completion(request, prompt),
                media_type="text/plain"
            )
        else:
            return await create_non_streaming_completion(request, prompt)

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except asyncio.TimeoutError:
        logger.error("Timeout while communicating with Rovo Dev")
        raise HTTPException(
            status_code=504,
            detail="Request timeout"
        )
    except Exception as e:
        logger.error(f"Error in chat completion: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error"
        )


async def create_non_streaming_completion(
    request: ChatCompletionRequest,
    prompt: str
) -> ChatCompletionResponse:
    """Create a non-streaming chat completion"""
    global rovo_client

    try:
        # Collect all response chunks
        response_content = ""
        full_response = ""

        # Send message with session clearing for new conversations
        async for chunk in rovo_client.send_message(prompt, clear_first=True):
            response_content += chunk
            full_response += chunk

        # If we got a response, clean it up
        if response_content.strip():
            cleaned_response = MessageConverter.clean_rovo_response(response_content)

            # Parse token usage
            usage_dict = rovo_client.parse_token_usage(full_response)
            usage = ChatCompletionUsage(**usage_dict)

            # Create response
            return ChatCompletionResponse.create(
                model=request.model,
                content=cleaned_response,
                usage=usage
            )
        else:
            # No response received, use fallback
            raise Exception("No response received from Rovo Dev")

    except Exception as e:
        logger.error(f"Error in Rovo Dev communication: {e}")

        # 诚实地告知用户 Rovo Dev 服务不可用
        # Honestly inform user that Rovo Dev service is unavailable
        error_message = f"抱歉，Rovo Dev 服务当前不可用。错误信息：{str(e)}"

        # 抛出 HTTP 异常而不是返回虚假响应
        # Raise HTTP exception instead of returning fake response
        raise HTTPException(
            status_code=503,
            detail=error_message
        )


async def stream_chat_completion(
    request: ChatCompletionRequest,
    prompt: str
) -> AsyncGenerator[str, None]:
    """Stream chat completion chunks"""
    global rovo_client

    completion_id = f"chatcmpl-{uuid.uuid4().hex[:29]}"

    # Send initial chunk
    initial_chunk = ChatCompletionStreamResponse.create_chunk(
        completion_id=completion_id,
        model=request.model,
        content=""
    )
    yield f"data: {initial_chunk.model_dump_json()}\n\n"

    # Stream response chunks
    try:
        async for chunk in rovo_client.send_message(prompt, clear_first=True):
            if chunk.strip():
                cleaned_chunk = MessageConverter.clean_rovo_response(chunk)
                if cleaned_chunk:
                    stream_chunk = ChatCompletionStreamResponse.create_chunk(
                        completion_id=completion_id,
                        model=request.model,
                        content=cleaned_chunk
                    )
                    yield f"data: {stream_chunk.model_dump_json()}\n\n"

    except Exception as e:
        logger.error(f"Error in streaming: {e}")
        error_chunk = ChatCompletionStreamResponse.create_chunk(
            completion_id=completion_id,
            model=request.model,
            content=f"Error: {str(e)}",
            finish_reason="stop"
        )
        yield f"data: {error_chunk.model_dump_json()}\n\n"

    # Send final chunk
    final_chunk = ChatCompletionStreamResponse.create_chunk(
        completion_id=completion_id,
        model=request.model,
        finish_reason="stop"
    )
    yield f"data: {final_chunk.model_dump_json()}\n\n"
    yield "data: [DONE]\n\n"


@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Handle HTTP exceptions in OpenAI format"""
    from fastapi.responses import JSONResponse
    error_response = ErrorResponse.create(
        message=exc.detail,
        error_type="invalid_request_error"
    )
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response.model_dump()
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions in OpenAI format"""
    from fastapi.responses import JSONResponse
    import traceback

    logger.error(f"Unhandled exception: {exc}")
    logger.error(f"Traceback: {traceback.format_exc()}")

    error_response = ErrorResponse.create(
        message=f"Internal server error: {str(exc)}",
        error_type="server_error"
    )
    return JSONResponse(
        status_code=500,
        content=error_response.model_dump()
    )
