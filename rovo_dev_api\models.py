"""
OpenAI-compatible data models for Rovo Dev API
"""

from typing import Any, Dict, List, Literal, Optional, Union
from pydantic import BaseModel, Field
import time
import uuid


class ChatMessage(BaseModel):
    """A chat message in the conversation"""
    role: Literal["system", "user", "assistant"] = Field(
        description="The role of the message author"
    )
    content: str = Field(description="The content of the message")
    name: Optional[str] = Field(None, description="The name of the author")


class ChatCompletionRequest(BaseModel):
    """Request model for chat completions"""
    model: str = Field(description="ID of the model to use")
    messages: List[ChatMessage] = Field(
        description="A list of messages comprising the conversation so far"
    )
    temperature: Optional[float] = Field(
        default=0.7,
        ge=0.0,
        le=2.0,
        description="Sampling temperature between 0 and 2"
    )
    top_p: Optional[float] = Field(
        default=1.0,
        ge=0.0,
        le=1.0,
        description="Nucleus sampling parameter"
    )
    n: Optional[int] = Field(
        default=1,
        ge=1,
        le=1,
        description="Number of completions to generate (only 1 supported)"
    )
    stream: Optional[bool] = Field(
        default=False,
        description="Whether to stream back partial progress"
    )
    stop: Optional[Union[str, List[str]]] = Field(
        default=None,
        description="Up to 4 sequences where the API will stop generating"
    )
    max_tokens: Optional[int] = Field(
        default=None,
        ge=1,
        description="Maximum number of tokens to generate"
    )
    presence_penalty: Optional[float] = Field(
        default=0.0,
        ge=-2.0,
        le=2.0,
        description="Presence penalty parameter"
    )
    frequency_penalty: Optional[float] = Field(
        default=0.0,
        ge=-2.0,
        le=2.0,
        description="Frequency penalty parameter"
    )
    logit_bias: Optional[Dict[str, float]] = Field(
        default=None,
        description="Logit bias for specific tokens"
    )
    user: Optional[str] = Field(
        default=None,
        description="A unique identifier representing your end-user"
    )


class ChatCompletionUsage(BaseModel):
    """Usage statistics for the completion request"""
    prompt_tokens: int = Field(description="Number of tokens in the prompt")
    completion_tokens: int = Field(description="Number of tokens in the completion")
    total_tokens: int = Field(description="Total number of tokens used")


class ChatCompletionChoice(BaseModel):
    """A completion choice"""
    index: int = Field(description="The index of this choice")
    message: ChatMessage = Field(description="The message generated by the model")
    finish_reason: Optional[Literal["stop", "length", "content_filter"]] = Field(
        description="The reason the model stopped generating tokens"
    )


class ChatCompletionResponse(BaseModel):
    """Response model for chat completions"""
    id: str = Field(description="A unique identifier for the chat completion")
    object: Literal["chat.completion"] = Field(
        default="chat.completion",
        description="The object type"
    )
    created: int = Field(description="The Unix timestamp of when the completion was created")
    model: str = Field(description="The model used for the completion")
    choices: List[ChatCompletionChoice] = Field(description="A list of completion choices")
    usage: ChatCompletionUsage = Field(description="Usage statistics for the completion")
    
    @classmethod
    def create(
        cls,
        model: str,
        content: str,
        usage: ChatCompletionUsage,
        finish_reason: str = "stop"
    ) -> "ChatCompletionResponse":
        """Create a chat completion response"""
        return cls(
            id=f"chatcmpl-{uuid.uuid4().hex[:29]}",
            created=int(time.time()),
            model=model,
            choices=[
                ChatCompletionChoice(
                    index=0,
                    message=ChatMessage(role="assistant", content=content),
                    finish_reason=finish_reason
                )
            ],
            usage=usage
        )


class ChatCompletionStreamChoice(BaseModel):
    """A streaming completion choice"""
    index: int = Field(description="The index of this choice")
    delta: Dict[str, Any] = Field(description="The delta message")
    finish_reason: Optional[Literal["stop", "length", "content_filter"]] = Field(
        default=None,
        description="The reason the model stopped generating tokens"
    )


class ChatCompletionStreamResponse(BaseModel):
    """Response model for streaming chat completions"""
    id: str = Field(description="A unique identifier for the chat completion")
    object: Literal["chat.completion.chunk"] = Field(
        default="chat.completion.chunk",
        description="The object type"
    )
    created: int = Field(description="The Unix timestamp of when the completion was created")
    model: str = Field(description="The model used for the completion")
    choices: List[ChatCompletionStreamChoice] = Field(description="A list of completion choices")
    
    @classmethod
    def create_chunk(
        cls,
        completion_id: str,
        model: str,
        content: str = "",
        finish_reason: Optional[str] = None
    ) -> "ChatCompletionStreamResponse":
        """Create a streaming chat completion chunk"""
        delta = {}
        if content:
            delta["content"] = content
        if finish_reason is None and content:
            delta["role"] = "assistant"
            
        return cls(
            id=completion_id,
            created=int(time.time()),
            model=model,
            choices=[
                ChatCompletionStreamChoice(
                    index=0,
                    delta=delta,
                    finish_reason=finish_reason
                )
            ]
        )


class ErrorResponse(BaseModel):
    """Error response model"""
    error: Dict[str, Any] = Field(description="Error details")
    
    @classmethod
    def create(cls, message: str, error_type: str = "invalid_request_error") -> "ErrorResponse":
        """Create an error response"""
        return cls(
            error={
                "message": message,
                "type": error_type,
                "code": None
            }
        )
