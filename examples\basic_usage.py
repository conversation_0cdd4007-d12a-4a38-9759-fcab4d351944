#!/usr/bin/env python3
"""
基本使用示例 - Rovo Dev API
"""

import openai
import asyncio
from typing import List, Dict


def setup_client(base_url: str = "http://localhost:8000/v1") -> openai.OpenAI:
    """设置 OpenAI 客户端"""
    return openai.OpenAI(
        base_url=base_url,
        api_key="dummy-key"  # Rovo Dev API 不需要真实的 API key
    )


def basic_chat_example():
    """基本聊天示例"""
    print("=== 基本聊天示例 ===")
    
    client = setup_client()
    
    response = client.chat.completions.create(
        model="rovo-dev",
        messages=[
            {"role": "system", "content": "你是一个有用的编程助手"},
            {"role": "user", "content": "请帮我写一个 Python 函数来计算斐波那契数列"}
        ],
        temperature=0.7
    )
    
    print("回答：")
    print(response.choices[0].message.content)
    print(f"\nToken 使用情况：{response.usage}")
    print("-" * 50)


def streaming_chat_example():
    """流式聊天示例"""
    print("=== 流式聊天示例 ===")
    
    client = setup_client()
    
    stream = client.chat.completions.create(
        model="rovo-dev",
        messages=[
            {"role": "user", "content": "解释一下什么是机器学习，请详细说明"}
        ],
        stream=True,
        temperature=0.7
    )
    
    print("流式回答：")
    for chunk in stream:
        if chunk.choices[0].delta.content is not None:
            print(chunk.choices[0].delta.content, end="", flush=True)
    
    print("\n" + "-" * 50)


def conversation_example():
    """对话示例"""
    print("=== 对话示例 ===")
    
    client = setup_client()
    
    # 模拟多轮对话
    messages = [
        {"role": "system", "content": "你是一个友好的助手，喜欢用简洁的方式回答问题"}
    ]
    
    conversation = [
        "你好！",
        "我想学习 Python 编程，有什么建议吗？",
        "那我应该从哪里开始呢？"
    ]
    
    for user_input in conversation:
        messages.append({"role": "user", "content": user_input})
        
        print(f"用户: {user_input}")
        
        response = client.chat.completions.create(
            model="rovo-dev",
            messages=messages,
            temperature=0.7
        )
        
        assistant_response = response.choices[0].message.content
        messages.append({"role": "assistant", "content": assistant_response})
        
        print(f"助手: {assistant_response}")
        print()
    
    print("-" * 50)


def code_generation_example():
    """代码生成示例"""
    print("=== 代码生成示例 ===")
    
    client = setup_client()
    
    response = client.chat.completions.create(
        model="rovo-dev",
        messages=[
            {"role": "system", "content": "你是一个专业的 Python 开发者，请提供高质量的代码和详细的注释"},
            {"role": "user", "content": """
请帮我创建一个 Python 类来管理待办事项列表，需要包含以下功能：
1. 添加任务
2. 删除任务
3. 标记任务为完成
4. 列出所有任务
5. 列出未完成的任务
"""}
        ],
        temperature=0.3  # 较低的温度以获得更一致的代码
    )
    
    print("生成的代码：")
    print(response.choices[0].message.content)
    print("-" * 50)


def error_handling_example():
    """错误处理示例"""
    print("=== 错误处理示例 ===")
    
    client = setup_client()
    
    try:
        # 尝试发送无效请求
        response = client.chat.completions.create(
            model="rovo-dev",
            messages=[],  # 空消息列表
            temperature=0.7
        )
    except Exception as e:
        print(f"捕获到错误: {e}")
    
    try:
        # 尝试使用无效参数
        response = client.chat.completions.create(
            model="rovo-dev",
            messages=[{"role": "user", "content": "Hello"}],
            n=2  # 不支持的参数
        )
    except Exception as e:
        print(f"捕获到错误: {e}")
    
    print("-" * 50)


def main():
    """主函数"""
    print("Rovo Dev API 使用示例")
    print("=" * 50)
    
    try:
        basic_chat_example()
        streaming_chat_example()
        conversation_example()
        code_generation_example()
        error_handling_example()
        
        print("所有示例执行完成！")
        
    except Exception as e:
        print(f"执行示例时出错: {e}")
        print("请确保 Rovo Dev API 服务正在运行在 http://localhost:8000")


if __name__ == "__main__":
    main()
