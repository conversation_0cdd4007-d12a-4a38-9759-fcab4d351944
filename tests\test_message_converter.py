"""
Tests for message converter
"""

import pytest
from rovo_dev_api.models import ChatMessage
from rovo_dev_api.message_converter import MessageConverter


def test_convert_single_user_message():
    """Test converting a single user message"""
    messages = [ChatMessage(role="user", content="Hello, how are you?")]
    result = MessageConverter.convert_messages_to_rovo_format(messages)
    assert result == "Hello, how are you?"


def test_convert_single_system_message():
    """Test converting a single system message"""
    messages = [ChatMessage(role="system", content="You are a helpful assistant")]
    result = MessageConverter.convert_messages_to_rovo_format(messages)
    assert "请按照以下指示进行回答：You are a helpful assistant" in result


def test_convert_conversation():
    """Test converting a conversation"""
    messages = [
        ChatMessage(role="system", content="You are a helpful assistant"),
        ChatMessage(role="user", content="Hello"),
        ChatMessage(role="assistant", content="Hi there!"),
        ChatMessage(role="user", content="How are you?")
    ]
    
    result = MessageConverter.convert_messages_to_rovo_format(messages)
    
    # Should contain system context
    assert "请按照以下指示进行回答：You are a helpful assistant" in result
    # Should contain conversation history
    assert "以下是我们之前的对话历史：" in result
    assert "用户: Hello" in result
    assert "助手: Hi there!" in result
    # Should contain current question
    assert "How are you?" in result


def test_extract_user_query():
    """Test extracting user query from messages"""
    messages = [
        ChatMessage(role="system", content="You are helpful"),
        ChatMessage(role="user", content="First question"),
        ChatMessage(role="assistant", content="First answer"),
        ChatMessage(role="user", content="Second question")
    ]
    
    query = MessageConverter.extract_user_query(messages)
    assert query == "Second question"


def test_has_system_context():
    """Test checking for system context"""
    messages_with_system = [
        ChatMessage(role="system", content="You are helpful"),
        ChatMessage(role="user", content="Hello")
    ]
    
    messages_without_system = [
        ChatMessage(role="user", content="Hello")
    ]
    
    assert MessageConverter.has_system_context(messages_with_system) is True
    assert MessageConverter.has_system_context(messages_without_system) is False


def test_get_conversation_length():
    """Test getting conversation length"""
    messages = [
        ChatMessage(role="system", content="You are helpful"),
        ChatMessage(role="user", content="Hello"),
        ChatMessage(role="assistant", content="Hi"),
        ChatMessage(role="user", content="How are you?")
    ]
    
    length = MessageConverter.get_conversation_length(messages)
    assert length == 3  # Excludes system message


def test_format_for_chinese_context():
    """Test formatting for Chinese context"""
    english_text = "Hello, how are you?"
    chinese_text = "你好，你怎么样？"
    
    english_result = MessageConverter.format_for_chinese_context(english_text)
    chinese_result = MessageConverter.format_for_chinese_context(chinese_text)
    
    assert "请用中文回答：" in english_result
    assert "请用中文回答：" not in chinese_result


def test_is_primarily_english():
    """Test English detection"""
    english_text = "Hello, how are you today?"
    chinese_text = "你好，今天怎么样？"
    mixed_text = "Hello 你好"
    
    assert MessageConverter._is_primarily_english(english_text) is True
    assert MessageConverter._is_primarily_english(chinese_text) is False
    assert MessageConverter._is_primarily_english(mixed_text) is True  # More English


def test_clean_rovo_response():
    """Test cleaning Rovo Dev response"""
    dirty_response = "│ Hello there! │\n├─────────────┤\n│ How are you? │"
    clean_response = MessageConverter.clean_rovo_response(dirty_response)
    
    assert "│" not in clean_response
    assert "├" not in clean_response
    assert "Hello there!" in clean_response
    assert "How are you?" in clean_response


def test_empty_messages():
    """Test handling empty messages"""
    result = MessageConverter.convert_messages_to_rovo_format([])
    assert result == ""
