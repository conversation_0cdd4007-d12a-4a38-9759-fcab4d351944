#!/usr/bin/env python3
"""
测试斐波那契函数请求
Test fibonacci function request
"""

import requests
import json

def test_fibonacci_request():
    """测试斐波那契函数请求"""
    response = requests.post(
        'http://localhost:8000/v1/chat/completions',
        headers={'Content-Type': 'application/json'},
        json={
            'model': 'rovo-dev',
            'messages': [{'role': 'user', 'content': '写一个斐波那契函数'}],
            'stream': False
        },
        timeout=30
    )
    
    print(f'Status: {response.status_code}')
    if response.status_code == 200:
        result = response.json()
        content = result['choices'][0]['message']['content']
        print(f'Response content:\n{content}')
        
        # 检查是否包含斐波那契相关内容
        if '斐波那契' in content or 'fibonacci' in content.lower():
            print('\n✅ 成功！响应包含斐波那契相关内容')
        else:
            print('\n❌ 响应不包含斐波那契相关内容')
    else:
        print(f'Error: {response.text}')

if __name__ == '__main__':
    test_fibonacci_request()
