"""
Integration tests for the API
"""

import pytest
from fastapi.testclient import <PERSON><PERSON><PERSON>
from unittest.mock import AsyncMock, MagicMock, patch

from rovo_dev_api.api import app
from rovo_dev_api.models import ChatMessage


@pytest.fixture
def client():
    """Test client fixture"""
    return TestClient(app)


@pytest.fixture
def mock_rovo_client():
    """Mock Rovo Dev client fixture"""
    mock_client = MagicMock()
    mock_client.session_active = True
    mock_client.send_message = AsyncMock()
    mock_client.parse_token_usage = MagicMock(return_value={
        "prompt_tokens": 10,
        "completion_tokens": 20,
        "total_tokens": 30
    })
    return mock_client


def test_root_endpoint(client):
    """Test root endpoint"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert data["message"] == "Rovo Dev API"
    assert data["version"] == "0.1.0"
    assert data["status"] == "running"


def test_list_models(client):
    """Test models endpoint"""
    response = client.get("/v1/models")
    assert response.status_code == 200
    data = response.json()
    assert data["object"] == "list"
    assert len(data["data"]) == 1
    assert data["data"][0]["id"] == "rovo-dev"


@patch('rovo_dev_api.api.rovo_client')
def test_chat_completion_success(mock_client_global, client, mock_rovo_client):
    """Test successful chat completion"""
    mock_client_global.return_value = mock_rovo_client
    
    # Mock the async generator
    async def mock_send_message(prompt):
        yield "Hello! "
        yield "How can I help you today?"
    
    mock_rovo_client.send_message = mock_send_message
    
    # Set the global client
    import rovo_dev_api.api
    rovo_dev_api.api.rovo_client = mock_rovo_client
    
    request_data = {
        "model": "rovo-dev",
        "messages": [
            {"role": "user", "content": "Hello"}
        ]
    }
    
    response = client.post("/v1/chat/completions", json=request_data)
    assert response.status_code == 200
    
    data = response.json()
    assert data["object"] == "chat.completion"
    assert data["model"] == "rovo-dev"
    assert len(data["choices"]) == 1
    assert data["choices"][0]["message"]["role"] == "assistant"
    assert "usage" in data


@patch('rovo_dev_api.api.rovo_client')
def test_chat_completion_empty_messages(mock_client_global, client, mock_rovo_client):
    """Test chat completion with empty messages"""
    mock_client_global.return_value = mock_rovo_client
    import rovo_dev_api.api
    rovo_dev_api.api.rovo_client = mock_rovo_client
    
    request_data = {
        "model": "rovo-dev",
        "messages": []
    }
    
    response = client.post("/v1/chat/completions", json=request_data)
    assert response.status_code == 400


@patch('rovo_dev_api.api.rovo_client')
def test_chat_completion_invalid_n(mock_client_global, client, mock_rovo_client):
    """Test chat completion with invalid n parameter"""
    mock_client_global.return_value = mock_rovo_client
    import rovo_dev_api.api
    rovo_dev_api.api.rovo_client = mock_rovo_client
    
    request_data = {
        "model": "rovo-dev",
        "messages": [{"role": "user", "content": "Hello"}],
        "n": 2
    }
    
    response = client.post("/v1/chat/completions", json=request_data)
    assert response.status_code == 400


def test_chat_completion_service_unavailable(client):
    """Test chat completion when service is unavailable"""
    # Clear the global client to simulate unavailable service
    import rovo_dev_api.api
    rovo_dev_api.api.rovo_client = None
    
    request_data = {
        "model": "rovo-dev",
        "messages": [{"role": "user", "content": "Hello"}]
    }
    
    response = client.post("/v1/chat/completions", json=request_data)
    assert response.status_code == 503


@patch('rovo_dev_api.api.rovo_client')
def test_chat_completion_streaming(mock_client_global, client, mock_rovo_client):
    """Test streaming chat completion"""
    mock_client_global.return_value = mock_rovo_client
    
    # Mock the async generator
    async def mock_send_message(prompt):
        yield "Hello! "
        yield "How can I help you?"
    
    mock_rovo_client.send_message = mock_send_message
    
    # Set the global client
    import rovo_dev_api.api
    rovo_dev_api.api.rovo_client = mock_rovo_client
    
    request_data = {
        "model": "rovo-dev",
        "messages": [{"role": "user", "content": "Hello"}],
        "stream": True
    }
    
    response = client.post("/v1/chat/completions", json=request_data)
    assert response.status_code == 200
    assert response.headers["content-type"] == "text/plain; charset=utf-8"
    
    # Check that response contains streaming data
    content = response.text
    assert "data:" in content
    assert "[DONE]" in content
