# Rovo Dev API

一个将 Atlassian Rovo Dev AI 助手封装成 OpenAI 兼容 Web API 的服务。

## 功能特性

- 🔌 **OpenAI 兼容接口**：完全兼容 OpenAI API 格式，支持 `/v1/chat/completions` 端点
- 🚀 **流式响应**：支持实时流式输出（`stream=true`）
- 🌐 **多语言支持**：原生支持中英文对话
- 📊 **Token 监控**：自动解析和返回 token 使用统计
- ⚡ **高性能**：基于 FastAPI 构建，支持异步处理
- 🛠️ **易于部署**：支持 Docker 容器化部署

## 快速开始

### 环境要求

- Python 3.8+
- 已安装并配置 Atlassian CLI (`acli`)
- 有效的 Rovo Dev 访问权限

### 安装

1. 克隆项目：
```bash
git clone <repository-url>
cd rovo-dev-api
```

2. 安装依赖：
```bash
pip install -r requirements.txt
```

3. 配置服务（可选）：
```bash
cp config.yaml.example config.yaml
# 编辑 config.yaml 根据需要修改配置
```

4. 启动服务：
```bash
python -m rovo_dev_api.main
```

服务将在 `http://localhost:8000` 启动。

### 使用示例

#### Python 客户端

```python
import openai

# 配置客户端
client = openai.OpenAI(
    base_url="http://localhost:8000/v1",
    api_key="dummy-key"  # Rovo Dev API 不需要真实的 API key
)

# 发送聊天请求
response = client.chat.completions.create(
    model="rovo-dev",
    messages=[
        {"role": "system", "content": "你是一个有用的编程助手"},
        {"role": "user", "content": "请帮我写一个 Python 函数来计算斐波那契数列"}
    ],
    temperature=0.7
)

print(response.choices[0].message.content)
```

#### 流式响应

```python
stream = client.chat.completions.create(
    model="rovo-dev",
    messages=[
        {"role": "user", "content": "解释一下什么是机器学习"}
    ],
    stream=True
)

for chunk in stream:
    if chunk.choices[0].delta.content is not None:
        print(chunk.choices[0].delta.content, end="")
```

#### cURL 示例

```bash
curl -X POST "http://localhost:8000/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "rovo-dev",
    "messages": [
      {"role": "user", "content": "Hello, how are you?"}
    ],
    "temperature": 0.7
  }'
```

## API 文档

### 端点

- `GET /` - 服务状态
- `GET /v1/models` - 列出可用模型
- `POST /v1/chat/completions` - 创建聊天完成

### 支持的参数

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `model` | string | - | 模型名称（使用 "rovo-dev"） |
| `messages` | array | - | 对话消息列表 |
| `temperature` | float | 0.7 | 采样温度 (0.0-2.0) |
| `stream` | boolean | false | 是否启用流式响应 |
| `max_tokens` | integer | null | 最大生成 token 数 |

### 响应格式

标准的 OpenAI API 响应格式，包含：
- `id`: 完成请求的唯一标识符
- `object`: 对象类型
- `created`: 创建时间戳
- `model`: 使用的模型
- `choices`: 完成选择列表
- `usage`: Token 使用统计

## 配置

### 配置文件 (config.yaml)

```yaml
# 服务器设置
server:
  host: "0.0.0.0"
  port: 8000
  workers: 1

# Rovo Dev 设置
rovo_dev:
  command: "acli rovodev run"
  timeout: 300
  working_directory: null

# API 设置
api:
  model_name: "rovo-dev"
  max_tokens: 4096
  temperature: 0.7

# 日志设置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
```

### 环境变量

也可以通过环境变量配置：

```bash
export SERVER__HOST=0.0.0.0
export SERVER__PORT=8000
export ROVO_DEV__TIMEOUT=300
export LOGGING__LEVEL=DEBUG
```

## Docker 部署

### 构建镜像

```bash
docker build -t rovo-dev-api .
```

### 运行容器

```bash
docker run -p 8000:8000 \
  -v ~/.atlassian:/root/.atlassian \
  rovo-dev-api
```

### Docker Compose

```yaml
version: '3.8'
services:
  rovo-dev-api:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ~/.atlassian:/root/.atlassian
    environment:
      - LOGGING__LEVEL=INFO
```

## 开发

### 运行测试

```bash
pytest tests/
```

### 开发模式启动

```bash
python -m rovo_dev_api.main --reload
```

### 代码格式化

```bash
black rovo_dev_api/
isort rovo_dev_api/
```

## 故障排除

### 常见问题

1. **Rovo Dev 服务不可用**
   - 确保已正确安装和配置 Atlassian CLI
   - 检查 Rovo Dev 访问权限
   - 验证网络连接

2. **Token 限制错误**
   - 检查 Rovo Dev 的每日 token 限制
   - 考虑实现请求队列或限流

3. **响应超时**
   - 增加 `timeout` 配置值
   - 检查 Rovo Dev 服务状态

### 日志

启用调试日志：
```bash
export LOGGING__LEVEL=DEBUG
python -m rovo_dev_api.main
```

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！

## 更新日志

### v0.1.0
- 初始版本
- 支持基本的 OpenAI 兼容接口
- 流式响应支持
- Token 使用统计
