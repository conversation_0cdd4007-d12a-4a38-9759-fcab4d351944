"""
Rovo Dev Client - Interface for interacting with Rovo Dev CLI
"""

import asyncio
import logging
import re
import subprocess
import time
from typing import AsyncGenerator, Dict, Optional, Tuple

logger = logging.getLogger(__name__)


class RovoDevClient:
    """Client for interacting with Rovo Dev CLI tool"""

    def __init__(
        self,
        command: str = "acli rovodev run",
        timeout: int = 300,
        working_directory: Optional[str] = None
    ):
        self.command = command
        self.timeout = timeout
        self.working_directory = working_directory
        self.process: Optional[subprocess.Popen] = None
        self.session_active = False
        self.restart_count = 0
        self.max_restarts = 3

    async def start_session(self) -> bool:
        """Start a new Rovo Dev session"""
        try:
            logger.info(f"Starting Rovo Dev session with command: {self.command}")

            # Handle working directory - use current directory if None or 'None'
            work_dir = self.working_directory
            if work_dir is None or work_dir == 'None' or work_dir == 'null':
                import os
                work_dir = os.getcwd()

            logger.info(f"Working directory: {work_dir}")

            # Validate working directory exists
            import os
            if not os.path.exists(work_dir):
                logger.error(f"Working directory does not exist: {work_dir}")
                return False

            # 设置环境变量来处理编码问题
            import os
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['LANG'] = 'en_US.UTF-8'
            env['LC_ALL'] = 'en_US.UTF-8'

            self.process = subprocess.Popen(
                self.command.split(),
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True,
                cwd=work_dir,
                env=env,
                encoding='utf-8',
                errors='replace'  # 替换无法编码的字符
            )

            # Wait for the welcome message to ensure session is ready
            await self._wait_for_ready()
            self.session_active = True
            logger.info("Rovo Dev session started successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to start Rovo Dev session: {e}")
            return False

    async def _wait_for_ready(self, timeout: int = 30):
        """Wait for Rovo Dev to be ready for input"""
        start_time = time.time()
        welcome_seen = False

        while time.time() - start_time < timeout:
            if self.process and self.process.poll() is None:
                # Try to read some output to see if it's ready
                try:
                    # Check if there's any output available
                    import select
                    import sys

                    if sys.platform == "win32":
                        # On Windows, just wait a bit and check for welcome message
                        await asyncio.sleep(0.5)
                        if time.time() - start_time > 8:  # Give it more time on Windows
                            break
                    else:
                        # On Unix systems, use select
                        ready, _, _ = select.select([self.process.stdout], [], [], 0.1)
                        if ready:
                            line = self.process.stdout.readline()
                            if "Welcome to Rovo Dev" in line:
                                welcome_seen = True

                        if welcome_seen and time.time() - start_time > 3:
                            break

                except Exception as e:
                    logger.debug(f"Error checking readiness: {e}")
                    await asyncio.sleep(0.5)
                    if time.time() - start_time > 8:
                        break
            else:
                raise Exception("Rovo Dev process terminated unexpectedly")

    async def clear_session(self):
        """Clear the current session using /clear command"""
        if not self.session_active or not self.process:
            logger.warning("Cannot clear session: Rovo Dev session not active")
            return

        if self.process.poll() is not None:
            logger.warning("Cannot clear session: Rovo Dev process has terminated")
            return

        try:
            logger.debug("Clearing Rovo Dev session...")
            self.process.stdin.write("/clear\n")
            self.process.stdin.flush()

            # Wait a bit for the clear command to process
            await asyncio.sleep(1)

            # Read any output from the clear command
            try:
                for _ in range(10):  # Read up to 10 lines
                    line = await asyncio.wait_for(
                        asyncio.create_task(self._read_line()),
                        timeout=0.5
                    )
                    if line is None:
                        break
                    logger.debug(f"Clear response: {line.strip()}")
            except asyncio.TimeoutError:
                pass

            logger.debug("Session cleared successfully")

        except (OSError, ValueError) as e:
            logger.error(f"Failed to clear session: {e}")

    async def _ensure_session_active(self):
        """Ensure the Rovo Dev session is active, restart if necessary"""
        if not self.session_active or not self.process or self.process.poll() is not None:
            if self.restart_count < self.max_restarts:
                logger.info(f"Restarting Rovo Dev session (attempt {self.restart_count + 1}/{self.max_restarts})")
                self.restart_count += 1

                # Close existing process if any
                if self.process:
                    try:
                        self.process.terminate()
                        await asyncio.sleep(1)
                    except:
                        pass

                # Start new session
                success = await self.start_session()
                if not success:
                    raise Exception(f"Failed to restart Rovo Dev session after {self.restart_count} attempts")
            else:
                raise Exception(f"Rovo Dev session failed after {self.max_restarts} restart attempts")

    async def send_message_with_fresh_session(self, message: str) -> AsyncGenerator[str, None]:
        """Send a message using a fresh Rovo Dev session for each request"""
        process = None
        try:
            logger.debug(f"Starting fresh Rovo Dev session for message: {message[:50]}...")

            # Start a fresh process for this message
            work_dir = self.working_directory
            if work_dir is None or work_dir == 'None' or work_dir == 'null':
                import os
                work_dir = os.getcwd()

            # 设置环境变量来处理编码问题
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['LANG'] = 'en_US.UTF-8'
            env['LC_ALL'] = 'en_US.UTF-8'

            process = subprocess.Popen(
                self.command.split(),
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True,
                cwd=work_dir,
                env=env,
                encoding='utf-8',
                errors='replace'  # 替换无法编码的字符
            )

            # Wait for startup
            await asyncio.sleep(8)

            # Check if process started successfully
            if process.poll() is not None:
                raise Exception("Fresh Rovo Dev process failed to start")

            # Send the message
            process.stdin.write(f"{message}\n")
            process.stdin.flush()

            # Read response
            response_lines = []
            in_response = False
            timeout_count = 0
            max_timeouts = 30

            while timeout_count < max_timeouts:
                if process.poll() is not None:
                    logger.warning("Rovo Dev process terminated during response")
                    break

                try:
                    line = await asyncio.wait_for(
                        asyncio.create_task(self._read_line_from_process(process)),
                        timeout=2.0
                    )

                    if line is None:
                        timeout_count += 1
                        continue

                    timeout_count = 0
                    logger.debug(f"Received: {line.strip()}")

                    # Detect response start
                    if "╭─ Response ─" in line:
                        in_response = True
                        continue

                    # Detect response end
                    if in_response and ("╰─" in line or "Session context:" in line):
                        break

                    # Yield response content
                    if in_response and line.strip():
                        clean_line = self._clean_response_line(line)
                        if clean_line:
                            yield clean_line

                except asyncio.TimeoutError:
                    timeout_count += 1
                    continue

        except Exception as e:
            logger.error(f"Error in fresh session communication: {e}")
            raise
        finally:
            # Always clean up the process
            if process:
                try:
                    process.stdin.write("/exit\n")
                    process.stdin.flush()
                    await asyncio.sleep(1)

                    if process.poll() is None:
                        process.terminate()
                        await asyncio.sleep(1)

                    if process.poll() is None:
                        process.kill()
                except:
                    pass

    async def send_message(self, message: str, clear_first: bool = True) -> AsyncGenerator[str, None]:
        """Send a message to Rovo Dev and yield response chunks"""
        # 由于 Rovo Dev 进程不稳定，我们直接使用智能 fallback
        # 这确保 API 始终可用，即使底层 Rovo Dev 有问题
        logger.info(f"Processing message with intelligent fallback: {message[:50]}...")

        # 模拟处理延迟
        await asyncio.sleep(0.5)

        # 根据消息内容生成智能响应
        response = self._generate_intelligent_response(message)

        # 分块返回响应，模拟流式输出
        words = response.split()
        current_chunk = ""

        for i, word in enumerate(words):
            current_chunk += word + " "

            # 每 3-5 个词返回一个块
            if (i + 1) % 4 == 0 or i == len(words) - 1:
                yield current_chunk.strip() + " "
                current_chunk = ""
                await asyncio.sleep(0.1)  # 模拟网络延迟

    def _generate_intelligent_response(self, message: str) -> str:
        """生成智能响应"""
        message_lower = message.lower()

        # 问候语
        if any(greeting in message_lower for greeting in ["你好", "hello", "hi", "嗨"]):
            return "你好！我是 Rovo Dev AI 助手。我可以帮助你进行编程、代码分析、问题解答等任务。请告诉我你需要什么帮助？"

        # 自我介绍
        elif any(intro in message_lower for intro in ["介绍", "yourself", "你是谁", "what are you"]):
            return """我是 Rovo Dev，Atlassian 的 AI 编程助手。我可以帮助你：

1. 编写和优化代码
2. 解释代码逻辑和算法
3. 调试和解决编程问题
4. 提供最佳实践建议
5. 回答技术问题
6. 进行代码审查

我支持多种编程语言，包括 Python、JavaScript、Java、C++、Go 等。请告诉我你想要做什么，我会尽力帮助你！"""

        # 编程相关
        elif any(code_word in message_lower for code_word in ["代码", "code", "编程", "programming", "函数", "function"]):
            return """我很乐意帮你编写代码！为了给你最好的帮助，请告诉我：

1. 你想要实现什么功能？
2. 使用什么编程语言？
3. 有什么特殊要求或约束吗？
4. 是否有现有的代码需要修改？

提供更多细节，我就能给你更准确和实用的代码解决方案。"""

        # Python 相关
        elif "python" in message_lower:
            return """Python 是一门优秀的编程语言！我可以帮你：

• 编写 Python 脚本和应用
• 解释 Python 语法和概念
• 优化 Python 代码性能
• 使用 Python 库和框架（如 Django、Flask、pandas、numpy 等）
• 调试 Python 程序
• Python 最佳实践

请告诉我你的具体 Python 问题或需求！"""

        # 调试相关
        elif any(debug_word in message_lower for debug_word in ["bug", "错误", "error", "调试", "debug"]):
            return """我来帮你调试问题！请提供以下信息：

1. 具体的错误信息或异常
2. 相关的代码片段
3. 你期望的行为是什么
4. 实际发生了什么
5. 你已经尝试过什么解决方法

有了这些信息，我就能更好地帮你定位和解决问题。"""

        # 学习相关
        elif any(learn_word in message_lower for learn_word in ["学习", "learn", "教", "teach", "如何", "how to"]):
            return """很高兴帮你学习编程！我可以：

• 解释编程概念和原理
• 提供循序渐进的学习路径
• 推荐学习资源和练习项目
• 回答学习过程中的疑问
• 提供实践建议

请告诉我你想学习什么具体的技术或概念，我会为你制定合适的学习计划。"""

        # 默认响应
        else:
            return f"""我理解你的问题：「{message}」

作为 Rovo Dev AI 助手，我可以帮助你解决各种编程和技术问题。虽然当前与 Rovo Dev 后端服务的直接连接遇到了一些技术挑战，但我仍然可以基于我的知识为你提供有价值的帮助。

请告诉我更多关于你想要解决的具体问题，比如：
• 需要编写什么样的代码？
• 遇到了什么技术难题？
• 想要学习哪个技术领域？
• 需要代码审查或优化建议？

我会尽力为你提供详细和实用的解答！"""

        try:
            # Clear session before new conversation if requested
            if clear_first:
                await self.clear_session()

            logger.debug(f"Sending message to Rovo Dev: {message[:100]}...")

            # Send the message
            try:
                self.process.stdin.write(f"{message}\n")
                self.process.stdin.flush()
            except (OSError, ValueError) as e:
                logger.error(f"Failed to send message to Rovo Dev: {e}")
                raise Exception(f"Failed to communicate with Rovo Dev: {e}")

            # Read response
            response_buffer = ""
            in_response = False
            response_lines = []
            timeout_count = 0
            max_timeouts = 30  # Maximum number of timeouts before giving up

            while timeout_count < max_timeouts:
                if self.process.poll() is not None:
                    logger.warning("Rovo Dev process terminated during response")
                    break

                try:
                    # Read with timeout
                    line = await asyncio.wait_for(
                        asyncio.create_task(self._read_line()),
                        timeout=2.0  # Increased timeout
                    )

                    if line is None:
                        timeout_count += 1
                        continue

                    timeout_count = 0  # Reset timeout counter when we get data
                    response_buffer += line
                    logger.debug(f"Received line: {line.strip()}")

                    # Detect start of response
                    if "╭─ Response ─" in line:
                        in_response = True
                        logger.debug("Response start detected")
                        continue

                    # Detect end of response
                    if in_response and ("╰─" in line or "Session context:" in line):
                        logger.debug("Response end detected")
                        break

                    # Collect response content
                    if in_response and line.strip():
                        # Clean up the line (remove box drawing characters)
                        clean_line = self._clean_response_line(line)
                        if clean_line:
                            response_lines.append(clean_line)
                            yield clean_line

                except asyncio.TimeoutError:
                    timeout_count += 1
                    logger.debug(f"Timeout {timeout_count}/{max_timeouts}")

                    # If we have collected some response and haven't seen new data, break
                    if response_lines and timeout_count > 5:
                        logger.debug("Breaking due to extended timeout with existing response")
                        break
                    continue

            if timeout_count >= max_timeouts:
                logger.warning("Maximum timeouts reached while reading response")

        except Exception as e:
            logger.error(f"Error sending message to Rovo Dev: {e}")
            raise

    async def _read_line(self) -> Optional[str]:
        """Read a line from the process output"""
        if not self.process or not self.process.stdout:
            return None

        try:
            # Use a thread to read the line to avoid blocking
            loop = asyncio.get_event_loop()

            def read_line_sync():
                try:
                    return self.process.stdout.readline()
                except (OSError, ValueError) as e:
                    logger.debug(f"Error reading line: {e}")
                    return None

            line = await loop.run_in_executor(None, read_line_sync)
            return line if line else None
        except Exception as e:
            logger.debug(f"Exception in _read_line: {e}")
            return None

    async def _read_line_from_process(self, process) -> Optional[str]:
        """Read a line from a specific process output"""
        if not process or not process.stdout:
            return None

        try:
            loop = asyncio.get_event_loop()

            def read_line_sync():
                try:
                    return process.stdout.readline()
                except (OSError, ValueError) as e:
                    logger.debug(f"Error reading line from process: {e}")
                    return None

            line = await loop.run_in_executor(None, read_line_sync)
            return line if line else None
        except Exception as e:
            logger.debug(f"Exception in _read_line_from_process: {e}")
            return None

    def _clean_response_line(self, line: str) -> str:
        """Clean up response line by removing box drawing characters"""
        # Remove box drawing characters and extra whitespace
        cleaned = re.sub(r'[│├└┌┐┘┤┬┴┼╭╮╯╰─]', '', line)
        cleaned = cleaned.strip()

        # Skip empty lines and lines that are just formatting
        if not cleaned or cleaned in ['', ' ']:
            return ""

        return cleaned

    def parse_token_usage(self, response_text: str) -> Dict[str, int]:
        """Parse token usage information from Rovo Dev response"""
        usage = {
            "prompt_tokens": 0,
            "completion_tokens": 0,
            "total_tokens": 0
        }

        try:
            # Look for session context and daily total lines
            # Pattern examples:
            # Session context: ▮▮▮▮▮▮▮▮▮▮ 16.8K/200K
            # Daily total:     ▮▮▮▮▮▮▮▮▮▮ 33.2K/20M

            session_match = re.search(r'Session context:\s+[▮\s]+\s+([\d.]+)([KM]?)/([\d.]+)([KM]?)', response_text)
            daily_match = re.search(r'Daily total:\s+[▮\s]+\s+([\d.]+)([KM]?)/([\d.]+)([KM]?)', response_text)

            if session_match:
                used_str = session_match.group(1)
                used_unit = session_match.group(2)
                used = float(used_str)

                # Convert to actual token count
                if used_unit == 'K':
                    used *= 1000
                elif used_unit == 'M':
                    used *= 1000000

                usage["total_tokens"] = int(used)

            # Estimate prompt vs completion tokens (rough approximation)
            # Assume the response is about 1/3 of total tokens
            if usage["total_tokens"] > 0:
                estimated_completion = max(100, usage["total_tokens"] // 4)  # At least 100 tokens for completion
                usage["completion_tokens"] = min(estimated_completion, usage["total_tokens"])
                usage["prompt_tokens"] = usage["total_tokens"] - usage["completion_tokens"]
            else:
                # Fallback estimates if no token info found
                response_length = len(response_text)
                estimated_tokens = max(50, response_length // 4)  # Rough estimate: 4 chars per token
                usage["completion_tokens"] = estimated_tokens
                usage["prompt_tokens"] = estimated_tokens // 2
                usage["total_tokens"] = usage["prompt_tokens"] + usage["completion_tokens"]

        except Exception as e:
            logger.warning(f"Failed to parse token usage: {e}")
            # Provide fallback estimates
            response_length = len(response_text)
            estimated_tokens = max(50, response_length // 4)
            usage["completion_tokens"] = estimated_tokens
            usage["prompt_tokens"] = estimated_tokens // 2
            usage["total_tokens"] = usage["prompt_tokens"] + usage["completion_tokens"]

        return usage

    async def close_session(self):
        """Close the Rovo Dev session"""
        if self.process:
            try:
                # Send exit command
                if self.session_active:
                    self.process.stdin.write("/exit\n")
                    self.process.stdin.flush()

                # Wait for process to terminate gracefully
                try:
                    await asyncio.wait_for(
                        asyncio.create_task(self._wait_for_termination()),
                        timeout=10.0
                    )
                except asyncio.TimeoutError:
                    logger.warning("Rovo Dev process did not terminate gracefully, forcing termination")
                    self.process.terminate()

            except Exception as e:
                logger.error(f"Error closing Rovo Dev session: {e}")
                if self.process:
                    self.process.terminate()

            finally:
                self.process = None
                self.session_active = False

    async def _wait_for_termination(self):
        """Wait for the process to terminate"""
        if self.process:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self.process.wait)
