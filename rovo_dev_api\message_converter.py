"""
消息格式转换器 - 将 OpenAI 格式转换为 Rovo Dev 格式
Message format converter for OpenAI to Rovo Dev format conversion
"""

import logging
from typing import List
from .models import ChatMessage

logger = logging.getLogger(__name__)


class MessageConverter:
    """
    消息转换器 - 将 OpenAI 格式的消息转换为 Rovo Dev 格式
    Converts OpenAI format messages to Rovo Dev format
    """

    @staticmethod
    def convert_messages_to_rovo_format(messages: List[ChatMessage]) -> str:
        """
        将 OpenAI 格式的消息转换为 Rovo Dev 的单个提示
        Convert OpenAI format messages to a single prompt for Rovo Dev

        Args:
            messages: ChatMessage 对象列表 / List of ChatMessage objects

        Returns:
            str: 格式化的 Rovo Dev 提示 / Formatted prompt for Rovo Dev
        """
        if not messages:
            return ""

        # 处理不同的对话模式
        # Handle different conversation patterns
        if len(messages) == 1:
            return MessageConverter._format_single_message(messages[0])

        return MessageConverter._format_conversation(messages)

    @staticmethod
    def _format_single_message(message: ChatMessage) -> str:
        """
        格式化单个消息
        Format a single message
        """
        if message.role == "user":
            return message.content
        elif message.role == "system":
            # 对于系统消息，将其作为上下文包含
            # For system messages, we'll include them as context
            return f"请按照以下指示进行回答：{message.content}"
        else:
            # 对于助手消息，作为用户输入处理
            # For assistant messages, treat as user input
            return message.content

    @staticmethod
    def _format_conversation(messages: List[ChatMessage]) -> str:
        """
        格式化多消息对话
        Format a multi-message conversation
        """
        formatted_parts = []
        system_context = []
        conversation_parts = []

        # 将系统消息与对话分离
        # Separate system messages from conversation
        for message in messages:
            if message.role == "system":
                system_context.append(message.content)
            else:
                conversation_parts.append(message)

        # 如果存在系统上下文，则添加
        # Add system context if present
        if system_context:
            context_text = " ".join(system_context)
            formatted_parts.append(f"请按照以下指示进行回答：{context_text}")
            formatted_parts.append("")  # 添加空行 / Add blank line

        # 处理对话历史
        # Handle conversation history
        if len(conversation_parts) > 1:
            formatted_parts.append("以下是我们之前的对话历史：")
            formatted_parts.append("")

            # 添加对话历史（除最后一条消息外的所有消息）
            # Add conversation history (all but the last message)
            for i, message in enumerate(conversation_parts[:-1]):
                if message.role == "user":
                    formatted_parts.append(f"用户: {message.content}")
                elif message.role == "assistant":
                    formatted_parts.append(f"助手: {message.content}")

            formatted_parts.append("")  # 添加空行 / Add blank line
            formatted_parts.append("现在请回答以下问题：")

        # 添加当前用户消息（最后一条消息）
        # Add the current user message (last message)
        last_message = conversation_parts[-1]
        if last_message.role == "user":
            formatted_parts.append(last_message.content)
        else:
            # 如果最后一条消息来自助手，将其视为继续请求
            # If last message is from assistant, treat it as a continuation request
            formatted_parts.append("请继续上述回答。")

        return "\n".join(formatted_parts)

    @staticmethod
    def extract_user_query(messages: List[ChatMessage]) -> str:
        """
        Extract the main user query from the messages

        Args:
            messages: List of ChatMessage objects

        Returns:
            str: The main user query
        """
        # Find the last user message
        for message in reversed(messages):
            if message.role == "user":
                return message.content

        # If no user message found, return the last message content
        if messages:
            return messages[-1].content

        return ""

    @staticmethod
    def has_system_context(messages: List[ChatMessage]) -> bool:
        """Check if messages contain system context"""
        return any(message.role == "system" for message in messages)

    @staticmethod
    def get_conversation_length(messages: List[ChatMessage]) -> int:
        """Get the number of non-system messages in the conversation"""
        return len([msg for msg in messages if msg.role != "system"])

    @staticmethod
    def format_for_chinese_context(text: str) -> str:
        """
        Format text to work better with Chinese language context

        Args:
            text: Input text

        Returns:
            str: Formatted text optimized for Chinese
        """
        # Add Chinese language hint if the text appears to be in English
        if MessageConverter._is_primarily_english(text):
            return f"请用中文回答：{text}"

        return text

    @staticmethod
    def _is_primarily_english(text: str) -> bool:
        """Check if text is primarily in English"""
        if not text:
            return False

        # Simple heuristic: if more than 70% of characters are ASCII letters
        ascii_letters = sum(1 for c in text if c.isascii() and c.isalpha())
        total_letters = sum(1 for c in text if c.isalpha())

        if total_letters == 0:
            return False

        return (ascii_letters / total_letters) > 0.7

    @staticmethod
    def clean_rovo_response(response: str) -> str:
        """
        清理 Rovo Dev 响应以适配 OpenAI 格式
        Clean up Rovo Dev response for OpenAI format

        Args:
            response: 来自 Rovo Dev 的原始响应 / Raw response from Rovo Dev

        Returns:
            str: 清理后的响应 / Cleaned response
        """
        # 移除任何剩余的框线字符
        # Remove any remaining box drawing characters
        cleaned = response.replace("│", "").replace("├", "").replace("└", "")
        cleaned = cleaned.replace("┌", "").replace("┐", "").replace("┘", "")
        cleaned = cleaned.replace("┤", "").replace("┬", "").replace("┴", "")
        cleaned = cleaned.replace("┼", "").replace("╭", "").replace("╮", "")
        cleaned = cleaned.replace("╯", "").replace("╰", "").replace("─", "")

        # 移除多余的空白并规范化换行符
        # Remove extra whitespace and normalize line breaks
        lines = [line.strip() for line in cleaned.split('\n')]
        lines = [line for line in lines if line]  # 移除空行 / Remove empty lines

        return '\n'.join(lines)
