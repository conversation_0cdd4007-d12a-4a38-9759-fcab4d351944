"""
Message format converter for OpenAI to Rovo Dev format conversion
"""

import logging
from typing import List
from .models import ChatMessage

logger = logging.getLogger(__name__)


class MessageConverter:
    """Converts OpenAI format messages to Rovo Dev format"""
    
    @staticmethod
    def convert_messages_to_rovo_format(messages: List[ChatMessage]) -> str:
        """
        Convert OpenAI format messages to a single prompt for Rovo Dev
        
        Args:
            messages: List of ChatMessage objects
            
        Returns:
            str: Formatted prompt for Rovo Dev
        """
        if not messages:
            return ""
        
        # Handle different conversation patterns
        if len(messages) == 1:
            return MessageConverter._format_single_message(messages[0])
        
        return MessageConverter._format_conversation(messages)
    
    @staticmethod
    def _format_single_message(message: ChatMessage) -> str:
        """Format a single message"""
        if message.role == "user":
            return message.content
        elif message.role == "system":
            # For system messages, we'll include them as context
            return f"请按照以下指示进行回答：{message.content}"
        else:
            # For assistant messages, treat as user input
            return message.content
    
    @staticmethod
    def _format_conversation(messages: List[ChatMessage]) -> str:
        """Format a multi-message conversation"""
        formatted_parts = []
        system_context = []
        conversation_parts = []
        
        # Separate system messages from conversation
        for message in messages:
            if message.role == "system":
                system_context.append(message.content)
            else:
                conversation_parts.append(message)
        
        # Add system context if present
        if system_context:
            context_text = " ".join(system_context)
            formatted_parts.append(f"请按照以下指示进行回答：{context_text}")
            formatted_parts.append("")  # Add blank line
        
        # Handle conversation history
        if len(conversation_parts) > 1:
            formatted_parts.append("以下是我们之前的对话历史：")
            formatted_parts.append("")
            
            # Add conversation history (all but the last message)
            for i, message in enumerate(conversation_parts[:-1]):
                if message.role == "user":
                    formatted_parts.append(f"用户: {message.content}")
                elif message.role == "assistant":
                    formatted_parts.append(f"助手: {message.content}")
            
            formatted_parts.append("")  # Add blank line
            formatted_parts.append("现在请回答以下问题：")
        
        # Add the current user message (last message)
        last_message = conversation_parts[-1]
        if last_message.role == "user":
            formatted_parts.append(last_message.content)
        else:
            # If last message is from assistant, treat it as a continuation request
            formatted_parts.append("请继续上述回答。")
        
        return "\n".join(formatted_parts)
    
    @staticmethod
    def extract_user_query(messages: List[ChatMessage]) -> str:
        """
        Extract the main user query from the messages
        
        Args:
            messages: List of ChatMessage objects
            
        Returns:
            str: The main user query
        """
        # Find the last user message
        for message in reversed(messages):
            if message.role == "user":
                return message.content
        
        # If no user message found, return the last message content
        if messages:
            return messages[-1].content
        
        return ""
    
    @staticmethod
    def has_system_context(messages: List[ChatMessage]) -> bool:
        """Check if messages contain system context"""
        return any(message.role == "system" for message in messages)
    
    @staticmethod
    def get_conversation_length(messages: List[ChatMessage]) -> int:
        """Get the number of non-system messages in the conversation"""
        return len([msg for msg in messages if msg.role != "system"])
    
    @staticmethod
    def format_for_chinese_context(text: str) -> str:
        """
        Format text to work better with Chinese language context
        
        Args:
            text: Input text
            
        Returns:
            str: Formatted text optimized for Chinese
        """
        # Add Chinese language hint if the text appears to be in English
        if MessageConverter._is_primarily_english(text):
            return f"请用中文回答：{text}"
        
        return text
    
    @staticmethod
    def _is_primarily_english(text: str) -> bool:
        """Check if text is primarily in English"""
        if not text:
            return False
        
        # Simple heuristic: if more than 70% of characters are ASCII letters
        ascii_letters = sum(1 for c in text if c.isascii() and c.isalpha())
        total_letters = sum(1 for c in text if c.isalpha())
        
        if total_letters == 0:
            return False
        
        return (ascii_letters / total_letters) > 0.7
    
    @staticmethod
    def clean_rovo_response(response: str) -> str:
        """
        Clean up Rovo Dev response for OpenAI format
        
        Args:
            response: Raw response from Rovo Dev
            
        Returns:
            str: Cleaned response
        """
        # Remove any remaining box drawing characters
        cleaned = response.replace("│", "").replace("├", "").replace("└", "")
        cleaned = cleaned.replace("┌", "").replace("┐", "").replace("┘", "")
        cleaned = cleaned.replace("┤", "").replace("┬", "").replace("┴", "")
        cleaned = cleaned.replace("┼", "").replace("╭", "").replace("╮", "")
        cleaned = cleaned.replace("╯", "").replace("╰", "").replace("─", "")
        
        # Remove extra whitespace and normalize line breaks
        lines = [line.strip() for line in cleaned.split('\n')]
        lines = [line for line in lines if line]  # Remove empty lines
        
        return '\n'.join(lines)
