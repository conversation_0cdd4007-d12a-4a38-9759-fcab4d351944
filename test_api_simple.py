#!/usr/bin/env python3
"""
简单的 API 测试
"""

import requests
import json


def test_api():
    """测试 API"""
    base_url = "http://127.0.0.1:8000"
    
    # 测试不同的消息
    test_cases = [
        {"role": "user", "content": "你好"},
        {"role": "user", "content": "请介绍一下你自己"},
        {"role": "user", "content": "你能帮我写代码吗？"},
        {"role": "user", "content": "Hello"},
        {"role": "user", "content": "What can you do?"}
    ]
    
    for i, message in enumerate(test_cases):
        print(f"\n--- 测试 {i+1} ---")
        print(f"消息: {message['content']}")
        
        payload = {
            "model": "rovo-dev",
            "messages": [message]
        }
        
        try:
            response = requests.post(
                f"{base_url}/v1/chat/completions",
                json=payload,
                timeout=30
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                content = data["choices"][0]["message"]["content"]
                print(f"回答: {content[:200]}...")
                print(f"Token 使用: {data['usage']}")
            else:
                print(f"错误: {response.text}")
                
        except Exception as e:
            print(f"请求失败: {e}")


if __name__ == "__main__":
    test_api()
