#!/usr/bin/env python3
"""
调试测试脚本
"""

import requests
import json
import traceback


def test_basic_endpoint():
    """测试基本端点"""
    print("测试基本端点...")
    try:
        response = requests.get("http://127.0.0.1:8000/")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"错误: {e}")
        return False


def test_models_endpoint():
    """测试模型端点"""
    print("\n测试模型端点...")
    try:
        response = requests.get("http://127.0.0.1:8000/v1/models")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"错误: {e}")
        return False


def test_chat_completion():
    """测试聊天完成"""
    print("\n测试聊天完成...")
    try:
        payload = {
            "model": "rovo-dev",
            "messages": [
                {"role": "user", "content": "Hello"}
            ]
        }
        
        print(f"发送请求: {json.dumps(payload, indent=2)}")
        
        response = requests.post(
            "http://127.0.0.1:8000/v1/chat/completions",
            json=payload,
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应: {response.text}")
        
        if response.status_code != 200:
            print("请求失败")
            return False
        
        # 尝试解析 JSON
        try:
            data = response.json()
            print(f"解析的 JSON: {json.dumps(data, indent=2)}")
        except json.JSONDecodeError as e:
            print(f"JSON 解析错误: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"错误: {e}")
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🔍 Rovo Dev API 调试测试")
    print("=" * 50)
    
    tests = [
        ("基本端点", test_basic_endpoint),
        ("模型端点", test_models_endpoint),
        ("聊天完成", test_chat_completion),
    ]
    
    passed = 0
    for name, test_func in tests:
        print(f"\n🧪 {name}")
        print("-" * 30)
        try:
            if test_func():
                print(f"✅ {name} 通过")
                passed += 1
            else:
                print(f"❌ {name} 失败")
        except Exception as e:
            print(f"❌ {name} 异常: {e}")
            traceback.print_exc()
    
    print(f"\n📊 结果: {passed}/{len(tests)} 通过")


if __name__ == "__main__":
    main()
