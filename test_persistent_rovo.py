#!/usr/bin/env python3
"""
测试持续的 Rovo Dev 对话
"""

import asyncio
import subprocess
import time
import logging

# 设置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


async def test_persistent_rovo_conversation():
    """测试持续的 Rovo Dev 对话"""
    print("🧪 测试持续的 Rovo Dev 对话...")

    process = None
    try:
        # 启动 Rovo Dev 进程
        print("启动 Rovo Dev 进程...")
        process = subprocess.Popen(
            ["acli", "rovodev", "run"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )

        print(f"进程 PID: {process.pid}")

        # 等待进程启动
        await asyncio.sleep(20)

        # 检查进程状态
        if process.poll() is not None:
            print(f"❌ 进程已退出，退出码: {process.poll()}")
            return False

        print("✅ 进程正在运行")

        # 定义测试对话
        conversations = [
            "你好",
            "请介绍一下你自己",
            "你能帮我写代码吗？"
        ]

        for i, message in enumerate(conversations):
            print(f"\n--- 对话 {i+1} ---")

            # 如果不是第一个对话，先清除会话
            if i > 0:
                print("发送 /clear 命令...")
                try:
                    process.stdin.write("/clear\n")
                    process.stdin.flush()
                    await asyncio.sleep(2)
                    print("✅ 会话已清除")
                except Exception as e:
                    print(f"❌ 清除会话失败: {e}")
                    continue

            # 发送消息
            print(f"发送消息: {message}")
            try:
                process.stdin.write(f"{message}\n")
                process.stdin.flush()
                print("✅ 消息发送成功")
            except Exception as e:
                print(f"❌ 发送消息失败: {e}")
                continue

            # 读取响应
            print("等待响应...")
            response_lines = []
            in_response = False
            timeout_count = 0
            max_timeout = 20

            while timeout_count < max_timeout:
                try:
                    # 检查进程状态
                    if process.poll() is not None:
                        print(f"❌ 进程在对话中退出，退出码: {process.poll()}")
                        return False

                    # 尝试读取一行
                    line = await asyncio.wait_for(
                        asyncio.create_task(read_line_async(process)),
                        timeout=1.0
                    )

                    if line is None:
                        timeout_count += 1
                        continue

                    timeout_count = 0
                    print(f"收到: {line.strip()}")

                    # 检测响应开始
                    if "╭─ Response ─" in line:
                        in_response = True
                        print("📝 响应开始")
                        continue

                    # 检测响应结束
                    if in_response and ("╰─" in line or "Session context:" in line):
                        print("📝 响应结束")
                        break

                    # 收集响应内容
                    if in_response and line.strip():
                        clean_line = line.strip()
                        # 移除边框字符
                        clean_line = clean_line.replace("│", "").strip()
                        if clean_line:
                            response_lines.append(clean_line)

                except asyncio.TimeoutError:
                    timeout_count += 1
                    if response_lines and timeout_count > 5:
                        print("⏰ 响应超时，但已收到部分内容")
                        break
                    continue

            if response_lines:
                print(f"✅ 收到响应 ({len(response_lines)} 行):")
                for line in response_lines[:3]:  # 只显示前3行
                    print(f"   {line}")
                if len(response_lines) > 3:
                    print(f"   ... (还有 {len(response_lines) - 3} 行)")
            else:
                print("❌ 未收到响应")

            # 等待一下再进行下一个对话
            await asyncio.sleep(2)

        print("\n✅ 所有对话测试完成")
        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

    finally:
        if process:
            print("关闭进程...")
            try:
                process.stdin.write("/exit\n")
                process.stdin.flush()
                await asyncio.sleep(3)

                if process.poll() is None:
                    process.terminate()
                    await asyncio.sleep(2)

                if process.poll() is None:
                    process.kill()

                print("✅ 进程已关闭")
            except Exception as e:
                print(f"关闭进程时出错: {e}")


async def read_line_async(process):
    """异步读取进程输出行"""
    loop = asyncio.get_event_loop()

    def read_line():
        try:
            return process.stdout.readline()
        except:
            return None

    return await loop.run_in_executor(None, read_line)


async def main():
    """主函数"""
    print("🔍 Rovo Dev 持续对话测试")
    print("=" * 50)

    success = await test_persistent_rovo_conversation()

    if success:
        print("\n🎉 测试成功！Rovo Dev 可以进行持续对话")
    else:
        print("\n❌ 测试失败")
        print("\n💡 可能的问题:")
        print("   1. Rovo Dev 进程不稳定")
        print("   2. 需要调整等待时间")
        print("   3. 响应格式解析需要改进")


if __name__ == "__main__":
    asyncio.run(main())
