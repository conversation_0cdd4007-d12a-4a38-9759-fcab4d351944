#!/usr/bin/env python3
"""
Rovo Dev API 演示脚本
"""

import requests
import json
import time


def test_api_connection(base_url="http://localhost:8000"):
    """测试 API 连接"""
    print("🔗 测试 API 连接...")

    try:
        response = requests.get(f"{base_url}/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API 服务正常运行")
            print(f"   版本: {data.get('version')}")
            print(f"   状态: {data.get('status')}")
            return True
        else:
            print(f"❌ API 响应异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 连接失败: {e}")
        return False


def test_models_endpoint(base_url="http://localhost:8000"):
    """测试模型端点"""
    print("\n📋 测试模型列表...")

    try:
        response = requests.get(f"{base_url}/v1/models")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 可用模型:")
            for model in data.get('data', []):
                print(f"   - {model.get('id')}")
            return True
        else:
            print(f"❌ 模型端点响应异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return False


def test_chat_completion(base_url="http://localhost:8000"):
    """测试聊天完成"""
    print("\n💬 测试聊天完成...")

    payload = {
        "model": "rovo-dev",
        "messages": [
            {"role": "system", "content": "你是一个有用的助手"},
            {"role": "user", "content": "请简单介绍一下你自己"}
        ],
        "temperature": 0.7
    }

    try:
        print("发送请求...")
        response = requests.post(
            f"{base_url}/v1/chat/completions",
            json=payload,
            timeout=30
        )

        if response.status_code == 200:
            data = response.json()
            print("✅ 聊天完成成功")
            print(f"   回答: {data['choices'][0]['message']['content'][:100]}...")
            print(f"   Token 使用: {data['usage']}")
            return True
        else:
            print(f"❌ 聊天完成失败: {response.status_code}")
            print(f"   错误: {response.text}")
            return False

    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return False


def test_streaming_chat(base_url="http://localhost:8000"):
    """测试流式聊天"""
    print("\n🌊 测试流式聊天...")

    payload = {
        "model": "rovo-dev",
        "messages": [
            {"role": "user", "content": "请用一句话介绍 Python 编程语言"}
        ],
        "stream": True,
        "temperature": 0.7
    }

    try:
        print("发送流式请求...")
        response = requests.post(
            f"{base_url}/v1/chat/completions",
            json=payload,
            stream=True,
            timeout=30
        )

        if response.status_code == 200:
            print("✅ 流式响应:")
            print("   ", end="", flush=True)

            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        data_str = line[6:]
                        if data_str == '[DONE]':
                            break
                        try:
                            data = json.loads(data_str)
                            if 'choices' in data and data['choices']:
                                delta = data['choices'][0].get('delta', {})
                                if 'content' in delta:
                                    print(delta['content'], end="", flush=True)
                        except json.JSONDecodeError:
                            continue

            print("\n✅ 流式聊天完成")
            return True
        else:
            print(f"❌ 流式聊天失败: {response.status_code}")
            return False

    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 Rovo Dev API 演示")
    print("=" * 50)

    base_url = "http://localhost:8000"

    # 测试序列
    tests = [
        test_api_connection,
        test_models_endpoint,
        test_chat_completion,
        test_streaming_chat
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test(base_url):
                passed += 1
            time.sleep(1)  # 短暂延迟
        except KeyboardInterrupt:
            print("\n\n⏹️ 演示被用户中断")
            break
        except Exception as e:
            print(f"\n❌ 测试异常: {e}")

    print(f"\n📊 测试结果: {passed}/{total} 通过")

    if passed == total:
        print("🎉 所有测试通过！API 工作正常")
    else:
        print("⚠️ 部分测试失败，请检查服务状态")
        print("\n💡 确保:")
        print("   1. Rovo Dev API 服务正在运行")
        print("   2. Atlassian CLI 已正确配置")
        print("   3. 有有效的 Rovo Dev 访问权限")


if __name__ == "__main__":
    main()
