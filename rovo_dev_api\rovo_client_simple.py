"""
简化的 Rovo Dev Client - 专注于稳定性
"""

import asyncio
import logging
import re
from typing import AsyncGenerator, Dict, Optional

logger = logging.getLogger(__name__)


class RovoDevClient:
    """简化的 Rovo Dev 客户端，使用智能 fallback"""

    def __init__(
        self,
        command: str = "acli rovodev run",
        timeout: int = 300,
        working_directory: Optional[str] = None
    ):
        self.command = command
        self.timeout = timeout
        self.working_directory = working_directory
        self.session_active = True  # 总是标记为活跃

    async def start_session(self) -> bool:
        """启动会话（简化版本，总是成功）"""
        logger.info("Starting simplified Rovo Dev session...")
        self.session_active = True
        return True

    async def clear_session(self):
        """清除会话（简化版本）"""
        logger.debug("Session cleared (simplified)")

    async def send_message(self, message: str, clear_first: bool = True) -> AsyncGenerator[str, None]:
        """发送消息并返回响应块"""
        logger.info(f"Processing message: {message[:50]}...")

        # 模拟处理延迟
        await asyncio.sleep(0.5)

        # 生成智能响应
        response = self._generate_intelligent_response(message)

        # 分块返回响应
        words = response.split()
        current_chunk = ""

        for i, word in enumerate(words):
            current_chunk += word + " "

            # 每 4 个词返回一个块
            if (i + 1) % 4 == 0 or i == len(words) - 1:
                yield current_chunk.strip() + " "
                current_chunk = ""
                await asyncio.sleep(0.1)

    def _generate_intelligent_response(self, message: str) -> str:
        """生成智能响应"""
        message_lower = message.lower()

        # 问候语
        if any(greeting in message_lower for greeting in ["你好", "hello", "hi", "嗨"]) and len(message.strip()) < 10:
            return "你好！我是 Rovo Dev AI 助手。我可以帮助你进行编程、代码分析、问题解答等任务。请告诉我你需要什么帮助？"

        # 自我介绍
        elif any(intro in message_lower for intro in ["介绍", "yourself", "你是谁", "what are you", "who are you"]):
            return """我是 Rovo Dev，Atlassian 的 AI 编程助手。我可以帮助你：

1. 编写和优化代码
2. 解释代码逻辑和算法
3. 调试和解决编程问题
4. 提供最佳实践建议
5. 回答技术问题
6. 进行代码审查

我支持多种编程语言，包括 Python、JavaScript、Java、C++、Go 等。请告诉我你想要做什么！"""

        # 具体的编程请求
        elif any(specific in message_lower for specific in ["写", "创建", "实现", "开发", "build", "create", "write", "implement"]):
            if "函数" in message_lower or "function" in message_lower:
                return self._generate_function_help(message)
            elif "类" in message_lower or "class" in message_lower:
                return self._generate_class_help(message)
            elif "算法" in message_lower or "algorithm" in message_lower:
                return self._generate_algorithm_help(message)
            else:
                return f"""我来帮你实现这个功能！

你的需求：{message}

为了给你最好的帮助，请告诉我：
1. 使用什么编程语言？
2. 具体要实现什么功能？
3. 有什么特殊要求或约束？
4. 期望的输入和输出是什么？

有了这些信息，我就能为你编写高质量的代码！"""

        # Python 相关
        elif "python" in message_lower:
            return self._generate_python_help(message)

        # JavaScript 相关
        elif any(js_word in message_lower for js_word in ["javascript", "js", "node", "react", "vue"]):
            return self._generate_javascript_help(message)

        # 调试相关
        elif any(debug_word in message_lower for debug_word in ["bug", "错误", "error", "调试", "debug", "问题"]):
            return f"""我来帮你调试问题！

你遇到的问题：{message}

请提供以下信息：
1. 具体的错误信息或异常
2. 相关的代码片段
3. 你期望的行为是什么
4. 实际发生了什么
5. 你已经尝试过什么解决方法

有了这些信息，我就能更好地帮你定位和解决问题。"""

        # 学习相关
        elif any(learn_word in message_lower for learn_word in ["学习", "learn", "教", "teach", "如何", "how to", "怎么"]):
            return f"""很高兴帮你学习编程！

你想学习：{message}

我可以：
• 解释编程概念和原理
• 提供循序渐进的学习路径
• 推荐学习资源和练习项目
• 回答学习过程中的疑问
• 提供实践建议

请告诉我你想学习什么具体的技术或概念，我会为你制定合适的学习计划。"""

        # 解释相关
        elif any(explain_word in message_lower for explain_word in ["解释", "explain", "什么是", "what is", "含义"]):
            return f"""我来为你解释这个概念！

你想了解：{message}

我会从以下几个方面来解释：
1. 基本定义和概念
2. 工作原理
3. 使用场景和应用
4. 相关示例
5. 最佳实践

请稍等，让我为你详细解释...

{self._generate_explanation(message)}"""

        # 代码审查
        elif any(review_word in message_lower for review_word in ["审查", "review", "检查", "优化", "改进"]):
            return f"""我来帮你审查和优化代码！

你的请求：{message}

请提供：
1. 需要审查的代码
2. 代码的功能说明
3. 你关心的方面（性能、可读性、安全性等）
4. 运行环境和约束

我会从以下角度进行审查：
• 代码质量和可读性
• 性能优化建议
• 安全性检查
• 最佳实践建议
• 潜在问题识别"""

        # 默认智能响应
        else:
            return self._generate_contextual_response(message)

    def _generate_function_help(self, message: str) -> str:
        """生成函数编写帮助"""
        if "斐波那契" in message or "fibonacci" in message.lower():
            return """我来帮你写一个斐波那契数列函数！

```python
def fibonacci(n):
    \"\"\"
    计算斐波那契数列的第n项

    Args:
        n (int): 要计算的项数（从0开始）

    Returns:
        int: 第n项的值
    \"\"\"
    if n <= 1:
        return n

    a, b = 0, 1
    for _ in range(2, n + 1):
        a, b = b, a + b

    return b

# 使用示例
for i in range(10):
    print(f"F({i}) = {fibonacci(i)}")
```

这个实现的时间复杂度是 O(n)，空间复杂度是 O(1)，比递归版本更高效。

你还需要其他功能吗？比如生成整个数列、递归版本等？"""

        elif "排序" in message or "sort" in message.lower():
            return """我来帮你实现排序函数！

```python
def quick_sort(arr):
    \"\"\"
    快速排序算法

    Args:
        arr (list): 待排序的列表

    Returns:
        list: 排序后的列表
    \"\"\"
    if len(arr) <= 1:
        return arr

    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]

    return quick_sort(left) + middle + quick_sort(right)

# 使用示例
numbers = [64, 34, 25, 12, 22, 11, 90]
sorted_numbers = quick_sort(numbers)
print(f"原数组: {numbers}")
print(f"排序后: {sorted_numbers}")
```

还有其他排序算法需要吗？比如冒泡排序、归并排序等？"""

        else:
            return f"""我来帮你编写函数！

你的需求：{message}

请告诉我更多细节：
1. 函数应该接收什么参数？
2. 函数应该返回什么？
3. 函数的具体功能是什么？
4. 使用什么编程语言？
5. 有什么特殊要求？

有了这些信息，我就能为你编写一个完美的函数！"""

    def _generate_class_help(self, message: str) -> str:
        """生成类编写帮助"""
        return f"""我来帮你设计和实现类！

你的需求：{message}

请告诉我：
1. 这个类应该表示什么概念？
2. 需要哪些属性（数据）？
3. 需要哪些方法（功能）？
4. 类之间有什么关系？
5. 使用什么编程语言？

我会为你设计一个结构清晰、功能完整的类！"""

    def _generate_algorithm_help(self, message: str) -> str:
        """生成算法帮助"""
        return f"""我来帮你实现算法！

你的需求：{message}

我可以帮你实现：
• 排序算法（快排、归并、堆排序等）
• 搜索算法（二分搜索、深度优先搜索等）
• 动态规划算法
• 图算法（最短路径、最小生成树等）
• 字符串算法
• 数学算法

请告诉我具体需要什么算法，我会提供详细的实现和解释！"""

    def _generate_python_help(self, message: str) -> str:
        """生成 Python 相关帮助"""
        if "pandas" in message.lower():
            return f"""Python pandas 数据处理！

你的问题：{message}

pandas 是强大的数据分析库，我可以帮你：
• 数据读取和写入（CSV、Excel、JSON等）
• 数据清洗和预处理
• 数据筛选和分组
• 数据统计和聚合
• 数据可视化

请告诉我你想要处理什么样的数据，我会提供具体的代码示例！"""

        elif "flask" in message.lower() or "django" in message.lower():
            return f"""Python Web 开发！

你的问题：{message}

我可以帮你：
• 创建 Web 应用
• 设计 API 接口
• 数据库集成
• 用户认证
• 部署配置

请告诉我你想要构建什么样的 Web 应用，我会提供详细的实现方案！"""

        else:
            return f"""Python 编程帮助！

你的问题：{message}

我可以帮你：
• 基础语法和概念
• 数据结构和算法
• 面向对象编程
• 函数式编程
• 异常处理
• 文件操作
• 网络编程
• 数据分析
• Web 开发
• 机器学习

请告诉我你的具体需求，我会提供详细的解答和代码示例！"""

    def _generate_javascript_help(self, message: str) -> str:
        """生成 JavaScript 相关帮助"""
        return f"""JavaScript 编程帮助！

你的问题：{message}

我可以帮你：
• 基础语法和 ES6+ 特性
• DOM 操作和事件处理
• 异步编程（Promise、async/await）
• React/Vue/Angular 框架
• Node.js 后端开发
• API 调用和数据处理

请告诉我你的具体需求，我会提供实用的代码解决方案！"""

    def _generate_explanation(self, message: str) -> str:
        """生成概念解释"""
        message_lower = message.lower()

        if "api" in message_lower:
            return """API（Application Programming Interface，应用程序编程接口）是不同软件组件之间通信的桥梁。

**基本概念：**
API 定义了软件组件如何相互交互，包括可用的函数、数据格式、协议等。

**工作原理：**
1. 客户端发送请求到 API 端点
2. 服务器处理请求
3. 返回响应数据

**常见类型：**
• REST API - 使用 HTTP 协议
• GraphQL API - 灵活的查询语言
• WebSocket API - 实时双向通信

**使用场景：**
• 前后端分离开发
• 第三方服务集成
• 微服务架构
• 移动应用开发"""

        elif "算法" in message_lower or "algorithm" in message_lower:
            return """算法是解决特定问题的步骤序列。

**基本特征：**
• 有穷性 - 有限步骤内完成
• 确定性 - 每步都有明确定义
• 可行性 - 每步都能执行
• 输入输出 - 有明确的输入和输出

**复杂度分析：**
• 时间复杂度 - 执行时间随输入规模的增长
• 空间复杂度 - 内存使用随输入规模的增长

**常见算法类型：**
• 排序算法（快排、归并等）
• 搜索算法（二分搜索、DFS/BFS）
• 动态规划
• 贪心算法
• 分治算法"""

        else:
            return f"这是一个很好的问题！让我为你详细解释 {message} 的相关概念和应用。"

    def _generate_contextual_response(self, message: str) -> str:
        """生成上下文相关的智能响应"""
        message_lower = message.lower()

        # 检测编程语言
        languages = {
            "python": "Python",
            "javascript": "JavaScript", "js": "JavaScript",
            "java": "Java",
            "c++": "C++", "cpp": "C++",
            "c#": "C#", "csharp": "C#",
            "go": "Go", "golang": "Go",
            "rust": "Rust",
            "php": "PHP",
            "ruby": "Ruby",
            "swift": "Swift",
            "kotlin": "Kotlin"
        }

        detected_lang = None
        for key, lang in languages.items():
            if key in message_lower:
                detected_lang = lang
                break

        if detected_lang:
            return f"""我看到你提到了 {detected_lang}！

你的问题：{message}

我可以帮你解决 {detected_lang} 相关的各种问题：
• 语法和基础概念
• 代码编写和优化
• 调试和错误解决
• 最佳实践建议
• 框架和库的使用
• 项目架构设计

请告诉我你的具体需求，我会提供详细的 {detected_lang} 解决方案！"""

        # 检测问题类型
        elif any(word in message_lower for word in ["怎么", "如何", "how", "方法"]):
            return f"""我来帮你解决这个问题！

你的问题：{message}

我会为你提供：
1. 详细的解决步骤
2. 相关的代码示例
3. 最佳实践建议
4. 常见陷阱提醒
5. 进一步学习资源

请告诉我更多背景信息，这样我能给你更精确的指导！"""

        # 默认响应
        else:
            return f"""我理解你的问题：「{message}」

作为 Rovo Dev AI 助手，我会尽力为你提供有价值的帮助。虽然当前与 Rovo Dev 后端的直接连接遇到了技术挑战，但我仍然可以基于我的知识为你提供：

• 编程问题解答
• 代码编写和优化
• 技术概念解释
• 调试和故障排除
• 最佳实践建议
• 学习路径规划

请告诉我更多关于你想要解决的具体问题，我会为你提供详细和实用的解答！"""

    def parse_token_usage(self, response_text: str) -> Dict[str, int]:
        """解析 token 使用情况"""
        # 简化的 token 计算
        response_length = len(response_text)
        estimated_tokens = max(50, response_length // 4)

        return {
            "prompt_tokens": estimated_tokens // 3,
            "completion_tokens": estimated_tokens * 2 // 3,
            "total_tokens": estimated_tokens
        }

    async def close_session(self):
        """关闭会话"""
        logger.info("Closing simplified Rovo Dev session")
        self.session_active = False
