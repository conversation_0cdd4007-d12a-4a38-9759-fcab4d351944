#!/usr/bin/env python3
"""
直接测试 Rovo Dev 命令行工具
"""

import subprocess
import time
import threading


def test_rovo_dev_direct():
    """直接测试 Rovo Dev"""
    print("🧪 直接测试 Rovo Dev...")
    
    try:
        # 启动 Rovo Dev 进程
        process = subprocess.Popen(
            ["acli", "rovodev", "run"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        print(f"进程 PID: {process.pid}")
        
        # 等待一下让进程启动
        time.sleep(5)
        
        # 检查进程状态
        poll_result = process.poll()
        if poll_result is not None:
            print(f"❌ 进程已退出，退出码: {poll_result}")
            stdout, stderr = process.communicate()
            print(f"标准输出: {stdout}")
            print(f"标准错误: {stderr}")
            return False
        
        print("✅ 进程正在运行")
        
        # 尝试发送一个简单的消息
        print("发送测试消息...")
        try:
            process.stdin.write("Hello\n")
            process.stdin.flush()
            print("✅ 消息发送成功")
        except Exception as e:
            print(f"❌ 发送消息失败: {e}")
            return False
        
        # 等待一下看看是否有响应
        time.sleep(3)
        
        # 检查进程是否还在运行
        poll_result = process.poll()
        if poll_result is not None:
            print(f"❌ 进程在发送消息后退出，退出码: {poll_result}")
            return False
        
        print("✅ 进程在发送消息后仍在运行")
        
        # 尝试读取一些输出
        def read_output():
            try:
                while True:
                    line = process.stdout.readline()
                    if line:
                        print(f"输出: {line.strip()}")
                    else:
                        break
            except Exception as e:
                print(f"读取输出错误: {e}")
        
        # 启动读取线程
        read_thread = threading.Thread(target=read_output)
        read_thread.daemon = True
        read_thread.start()
        
        # 等待一下
        time.sleep(5)
        
        # 发送退出命令
        try:
            process.stdin.write("/exit\n")
            process.stdin.flush()
            print("发送退出命令")
        except Exception as e:
            print(f"发送退出命令失败: {e}")
        
        # 等待进程结束
        try:
            process.wait(timeout=10)
            print("✅ 进程正常退出")
            return True
        except subprocess.TimeoutExpired:
            print("⚠️ 进程未在超时时间内退出，强制终止")
            process.terminate()
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🔍 Rovo Dev 直接测试")
    print("=" * 50)
    
    success = test_rovo_dev_direct()
    
    if success:
        print("\n✅ Rovo Dev 直接测试成功")
    else:
        print("\n❌ Rovo Dev 直接测试失败")
        print("\n💡 可能的问题:")
        print("   1. Rovo Dev 会话可能需要特定的工作目录")
        print("   2. Rovo Dev 可能需要特定的环境变量")
        print("   3. Rovo Dev 可能在没有交互时自动退出")


if __name__ == "__main__":
    main()
