# Rovo Dev API Configuration

# Server settings
server:
  host: "0.0.0.0"
  port: 8000
  workers: 1
  
# Rovo Dev settings
rovo_dev:
  command: "acli rovodev run"
  timeout: 300  # 5 minutes
  working_directory: null  # Use current directory if null
  
# API settings
api:
  model_name: "rovo-dev"
  max_tokens: 4096
  temperature: 0.7
  
# Logging
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
