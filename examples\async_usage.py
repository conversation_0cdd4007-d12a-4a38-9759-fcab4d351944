#!/usr/bin/env python3
"""
异步使用示例 - Rovo Dev API
"""

import asyncio
import aiohttp
import json
from typing import List, Dict, AsyncGenerator


class AsyncRovoDevClient:
    """异步 Rovo Dev API 客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: str = "rovo-dev",
        temperature: float = 0.7,
        stream: bool = False
    ) -> Dict:
        """发送聊天完成请求"""
        url = f"{self.base_url}/v1/chat/completions"
        
        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "stream": stream
        }
        
        if stream:
            return await self._stream_completion(url, payload)
        else:
            return await self._regular_completion(url, payload)
    
    async def _regular_completion(self, url: str, payload: Dict) -> Dict:
        """常规完成请求"""
        async with self.session.post(url, json=payload) as response:
            response.raise_for_status()
            return await response.json()
    
    async def _stream_completion(self, url: str, payload: Dict) -> AsyncGenerator[Dict, None]:
        """流式完成请求"""
        async with self.session.post(url, json=payload) as response:
            response.raise_for_status()
            
            async for line in response.content:
                line = line.decode('utf-8').strip()
                if line.startswith('data: '):
                    data = line[6:]  # 移除 'data: ' 前缀
                    if data == '[DONE]':
                        break
                    try:
                        yield json.loads(data)
                    except json.JSONDecodeError:
                        continue


async def basic_async_example():
    """基本异步示例"""
    print("=== 基本异步示例 ===")
    
    async with AsyncRovoDevClient() as client:
        response = await client.chat_completion(
            messages=[
                {"role": "system", "content": "你是一个有用的助手"},
                {"role": "user", "content": "请简单介绍一下 Python 的异步编程"}
            ],
            temperature=0.7
        )
        
        print("回答：")
        print(response["choices"][0]["message"]["content"])
        print(f"Token 使用: {response['usage']}")
        print("-" * 50)


async def concurrent_requests_example():
    """并发请求示例"""
    print("=== 并发请求示例 ===")
    
    questions = [
        "什么是机器学习？",
        "什么是深度学习？",
        "什么是人工智能？",
        "什么是自然语言处理？"
    ]
    
    async with AsyncRovoDevClient() as client:
        # 创建并发任务
        tasks = []
        for question in questions:
            task = client.chat_completion(
                messages=[{"role": "user", "content": question}],
                temperature=0.7
            )
            tasks.append(task)
        
        # 等待所有任务完成
        responses = await asyncio.gather(*tasks)
        
        # 显示结果
        for question, response in zip(questions, responses):
            print(f"问题: {question}")
            print(f"回答: {response['choices'][0]['message']['content'][:100]}...")
            print()
        
        print("-" * 50)


async def streaming_async_example():
    """异步流式示例"""
    print("=== 异步流式示例 ===")
    
    async with AsyncRovoDevClient() as client:
        print("问题: 请详细解释什么是 FastAPI 框架")
        print("流式回答: ", end="", flush=True)
        
        async for chunk in await client.chat_completion(
            messages=[
                {"role": "user", "content": "请详细解释什么是 FastAPI 框架"}
            ],
            stream=True
        ):
            if "choices" in chunk and chunk["choices"]:
                delta = chunk["choices"][0].get("delta", {})
                if "content" in delta:
                    print(delta["content"], end="", flush=True)
        
        print("\n" + "-" * 50)


async def conversation_async_example():
    """异步对话示例"""
    print("=== 异步对话示例 ===")
    
    async with AsyncRovoDevClient() as client:
        messages = [
            {"role": "system", "content": "你是一个编程导师，请用简洁明了的方式回答问题"}
        ]
        
        conversation_flow = [
            "我想学习 Web 开发，应该从哪里开始？",
            "那 HTML、CSS 和 JavaScript 我应该按什么顺序学？",
            "学完这些基础后，我应该学习什么框架？"
        ]
        
        for user_input in conversation_flow:
            messages.append({"role": "user", "content": user_input})
            
            print(f"用户: {user_input}")
            
            response = await client.chat_completion(
                messages=messages,
                temperature=0.7
            )
            
            assistant_response = response["choices"][0]["message"]["content"]
            messages.append({"role": "assistant", "content": assistant_response})
            
            print(f"助手: {assistant_response}")
            print()
        
        print("-" * 50)


async def batch_processing_example():
    """批处理示例"""
    print("=== 批处理示例 ===")
    
    # 模拟需要处理的代码片段
    code_snippets = [
        "def hello(): print('Hello World')",
        "for i in range(10): print(i)",
        "import json; data = {'key': 'value'}",
        "class MyClass: def __init__(self): pass"
    ]
    
    async with AsyncRovoDevClient() as client:
        # 为每个代码片段创建解释任务
        tasks = []
        for snippet in code_snippets:
            task = client.chat_completion(
                messages=[
                    {"role": "system", "content": "你是一个 Python 代码解释器，请简洁地解释代码的功能"},
                    {"role": "user", "content": f"请解释这段 Python 代码：{snippet}"}
                ],
                temperature=0.3
            )
            tasks.append(task)
        
        # 批量处理
        print("正在批量处理代码解释...")
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 显示结果
        for snippet, response in zip(code_snippets, responses):
            print(f"代码: {snippet}")
            if isinstance(response, Exception):
                print(f"错误: {response}")
            else:
                print(f"解释: {response['choices'][0]['message']['content']}")
            print()
        
        print("-" * 50)


async def error_handling_async_example():
    """异步错误处理示例"""
    print("=== 异步错误处理示例 ===")
    
    async with AsyncRovoDevClient() as client:
        try:
            # 尝试发送到错误的端点
            client.base_url = "http://localhost:9999"  # 错误的端口
            response = await client.chat_completion(
                messages=[{"role": "user", "content": "Hello"}]
            )
        except Exception as e:
            print(f"连接错误: {e}")
        
        # 恢复正确的 URL
        client.base_url = "http://localhost:8000"
        
        try:
            # 尝试发送无效请求
            response = await client.chat_completion(
                messages=[]  # 空消息
            )
        except Exception as e:
            print(f"请求错误: {e}")
        
        print("-" * 50)


async def main():
    """主异步函数"""
    print("Rovo Dev API 异步使用示例")
    print("=" * 50)
    
    try:
        await basic_async_example()
        await concurrent_requests_example()
        await streaming_async_example()
        await conversation_async_example()
        await batch_processing_example()
        await error_handling_async_example()
        
        print("所有异步示例执行完成！")
        
    except Exception as e:
        print(f"执行异步示例时出错: {e}")
        print("请确保 Rovo Dev API 服务正在运行在 http://localhost:8000")


if __name__ == "__main__":
    asyncio.run(main())
