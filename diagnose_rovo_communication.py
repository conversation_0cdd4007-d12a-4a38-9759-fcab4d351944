#!/usr/bin/env python3
"""
详细诊断 Rovo Dev 通信问题
"""

import subprocess
import asyncio
import time
import threading
import queue
import logging
import os

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class RovoDevDiagnostic:
    """Rovo Dev 诊断工具"""
    
    def __init__(self):
        self.process = None
        self.output_queue = queue.Queue()
        self.error_queue = queue.Queue()
        
    def start_output_reader(self, stream, output_queue, name):
        """在单独线程中读取输出"""
        try:
            while True:
                line = stream.readline()
                if not line:
                    logger.info(f"{name} stream ended")
                    break
                output_queue.put((time.time(), line.strip()))
                logger.debug(f"{name}: {line.strip()}")
        except Exception as e:
            logger.error(f"Error reading {name}: {e}")
            output_queue.put((time.time(), f"ERROR: {e}"))
    
    async def test_rovo_dev_lifecycle(self):
        """测试 Rovo Dev 完整生命周期"""
        logger.info("🔍 开始 Rovo Dev 生命周期诊断")
        
        try:
            # 1. 启动进程
            logger.info("1️⃣ 启动 Rovo Dev 进程...")
            self.process = subprocess.Popen(
                ["acli", "rovodev", "run"],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=0,  # 无缓冲
                universal_newlines=True,
                cwd=os.getcwd()
            )
            
            logger.info(f"✅ 进程启动，PID: {self.process.pid}")
            
            # 2. 启动输出读取线程
            stdout_thread = threading.Thread(
                target=self.start_output_reader,
                args=(self.process.stdout, self.output_queue, "STDOUT"),
                daemon=True
            )
            stderr_thread = threading.Thread(
                target=self.start_output_reader,
                args=(self.process.stderr, self.error_queue, "STDERR"),
                daemon=True
            )
            
            stdout_thread.start()
            stderr_thread.start()
            
            # 3. 等待启动完成
            logger.info("2️⃣ 等待进程启动完成...")
            startup_timeout = 15
            start_time = time.time()
            welcome_seen = False
            
            while time.time() - start_time < startup_timeout:
                # 检查进程状态
                if self.process.poll() is not None:
                    logger.error(f"❌ 进程意外退出，退出码: {self.process.poll()}")
                    return False
                
                # 检查输出
                try:
                    timestamp, line = self.output_queue.get_nowait()
                    if "Welcome to Rovo Dev" in line:
                        welcome_seen = True
                        logger.info("✅ 看到欢迎消息")
                        break
                except queue.Empty:
                    pass
                
                await asyncio.sleep(0.5)
            
            if not welcome_seen:
                logger.warning("⚠️ 未看到欢迎消息，但继续测试")
            
            # 4. 测试进程状态
            logger.info("3️⃣ 检查进程状态...")
            poll_result = self.process.poll()
            if poll_result is not None:
                logger.error(f"❌ 进程已退出，退出码: {poll_result}")
                return False
            
            logger.info("✅ 进程仍在运行")
            
            # 5. 测试 stdin 可写性
            logger.info("4️⃣ 测试 stdin 可写性...")
            try:
                # 检查 stdin 是否可写
                if self.process.stdin.closed:
                    logger.error("❌ stdin 已关闭")
                    return False
                
                logger.info("✅ stdin 可用")
                
                # 6. 尝试发送简单命令
                logger.info("5️⃣ 发送测试消息...")
                test_message = "hello\n"
                
                # 记录发送前的状态
                logger.info(f"发送前进程状态: {self.process.poll()}")
                logger.info(f"发送前 stdin 状态: closed={self.process.stdin.closed}")
                
                # 发送消息
                self.process.stdin.write(test_message)
                self.process.stdin.flush()
                logger.info("✅ 消息发送成功")
                
                # 7. 等待响应
                logger.info("6️⃣ 等待响应...")
                response_timeout = 10
                response_start = time.time()
                response_seen = False
                
                while time.time() - response_start < response_timeout:
                    # 检查进程状态
                    poll_result = self.process.poll()
                    if poll_result is not None:
                        logger.error(f"❌ 进程在发送消息后退出，退出码: {poll_result}")
                        break
                    
                    # 检查输出
                    try:
                        timestamp, line = self.output_queue.get_nowait()
                        logger.info(f"收到输出: {line}")
                        if "Response" in line or len(line) > 10:
                            response_seen = True
                    except queue.Empty:
                        pass
                    
                    # 检查错误输出
                    try:
                        timestamp, line = self.error_queue.get_nowait()
                        logger.error(f"错误输出: {line}")
                    except queue.Empty:
                        pass
                    
                    await asyncio.sleep(0.5)
                
                if response_seen:
                    logger.info("✅ 收到响应")
                else:
                    logger.warning("⚠️ 未收到明显响应")
                
                # 8. 最终状态检查
                logger.info("7️⃣ 最终状态检查...")
                final_poll = self.process.poll()
                if final_poll is not None:
                    logger.error(f"❌ 进程最终状态: 已退出，退出码: {final_poll}")
                    return False
                else:
                    logger.info("✅ 进程仍在运行")
                
                return True
                
            except BrokenPipeError as e:
                logger.error(f"❌ Broken pipe 错误: {e}")
                return False
            except OSError as e:
                logger.error(f"❌ OS 错误: {e}")
                return False
            except Exception as e:
                logger.error(f"❌ 未知错误: {e}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 诊断过程中出错: {e}")
            return False
        
        finally:
            # 清理
            if self.process:
                logger.info("🧹 清理进程...")
                try:
                    if self.process.poll() is None:
                        self.process.stdin.write("/exit\n")
                        self.process.stdin.flush()
                        await asyncio.sleep(2)
                        
                        if self.process.poll() is None:
                            self.process.terminate()
                            await asyncio.sleep(1)
                            
                        if self.process.poll() is None:
                            self.process.kill()
                            
                    logger.info("✅ 进程已清理")
                except Exception as e:
                    logger.error(f"清理进程时出错: {e}")
    
    async def test_multiple_sessions(self):
        """测试多个会话"""
        logger.info("🔄 测试多个独立会话...")
        
        for i in range(3):
            logger.info(f"--- 会话 {i+1} ---")
            success = await self.test_rovo_dev_lifecycle()
            if not success:
                logger.error(f"❌ 会话 {i+1} 失败")
                return False
            logger.info(f"✅ 会话 {i+1} 成功")
            await asyncio.sleep(2)
        
        return True


async def main():
    """主函数"""
    print("🔍 Rovo Dev 通信问题详细诊断")
    print("=" * 60)
    
    diagnostic = RovoDevDiagnostic()
    
    # 单次测试
    logger.info("开始单次生命周期测试...")
    success = await diagnostic.test_rovo_dev_lifecycle()
    
    if success:
        logger.info("✅ 单次测试成功")
        
        # 多次测试
        logger.info("开始多次会话测试...")
        multi_success = await diagnostic.test_multiple_sessions()
        
        if multi_success:
            logger.info("🎉 所有测试成功！")
        else:
            logger.error("❌ 多次会话测试失败")
    else:
        logger.error("❌ 单次测试失败")
    
    print("\n📋 诊断完成")
    print("请查看上面的详细日志来了解问题所在")


if __name__ == "__main__":
    asyncio.run(main())
