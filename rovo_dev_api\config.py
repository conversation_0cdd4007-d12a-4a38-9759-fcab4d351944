"""
Configuration management for Rovo Dev API
"""

import os
import yaml
from functools import lru_cache
from typing import Optional
from pydantic import BaseModel
from pydantic_settings import BaseSettings


class ServerConfig(BaseModel):
    """Server configuration"""
    host: str = "0.0.0.0"
    port: int = 8000
    workers: int = 1


class RovoDevConfig(BaseModel):
    """Rovo Dev configuration"""
    command: str = "acli rovodev run"
    timeout: int = 300
    working_directory: Optional[str] = None


class APIConfig(BaseModel):
    """API configuration"""
    model_config = {"protected_namespaces": ()}

    model_name: str = "rovo-dev"
    max_tokens: int = 4096
    temperature: float = 0.7


class LoggingConfig(BaseModel):
    """Logging configuration"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"


class Settings(BaseSettings):
    """Application settings"""
    server: ServerConfig = ServerConfig()
    rovo_dev: RovoDevConfig = RovoDevConfig()
    api: APIConfig = APIConfig()
    logging: LoggingConfig = LoggingConfig()

    class Config:
        env_file = ".env"
        env_nested_delimiter = "__"
        case_sensitive = False


def load_config_from_yaml(config_path: str = "config.yaml") -> dict:
    """Load configuration from YAML file"""
    if not os.path.exists(config_path):
        return {}

    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f) or {}
    except Exception as e:
        print(f"Warning: Failed to load config from {config_path}: {e}")
        return {}


@lru_cache()
def get_settings() -> Settings:
    """Get application settings (cached)"""
    # Load from YAML first
    yaml_config = load_config_from_yaml()

    # Create settings with YAML config as defaults
    if yaml_config:
        # Flatten nested config for pydantic
        flattened = {}
        for section, values in yaml_config.items():
            if isinstance(values, dict):
                for key, value in values.items():
                    flattened[f"{section}__{key}"] = value
            else:
                flattened[section] = values

        # Set environment variables temporarily
        original_env = {}
        for key, value in flattened.items():
            env_key = key.upper()
            if env_key not in os.environ:
                original_env[env_key] = os.environ.get(env_key)
                os.environ[env_key] = str(value)

        try:
            settings = Settings()
        finally:
            # Restore original environment
            for key, value in original_env.items():
                if value is None:
                    os.environ.pop(key, None)
                else:
                    os.environ[key] = value
    else:
        settings = Settings()

    return settings


def setup_logging():
    """Setup logging configuration"""
    import logging

    settings = get_settings()

    logging.basicConfig(
        level=getattr(logging, settings.logging.level.upper()),
        format=settings.logging.format
    )

    # Set specific loggers
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("fastapi").setLevel(logging.INFO)
    logging.getLogger("rovo_dev_api").setLevel(
        getattr(logging, settings.logging.level.upper())
    )
